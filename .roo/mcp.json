{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "alwaysAllow": ["browser_close", "browser_wait", "browser_resize", "browser_console_messages", "browser_handle_dialog", "browser_file_upload", "browser_install", "browser_press_key", "browser_navigate", "browser_navigate_back", "browser_navigate_forward", "browser_network_requests", "browser_snapshot", "browser_pdf_save", "browser_click", "browser_drag", "browser_hover", "browser_type", "browser_select_option", "browser_take_screenshot", "browser_tab_list", "browser_tab_new", "browser_tab_select", "browser_tab_close", "browser_generate_playwright_test"], "disabled": true}, "searxng": {"command": "npx", "args": ["-y", "mcp-searxng"], "env": {"SEARXNG_URL": "https://search.ibootz.com/"}, "alwaysAllow": ["searxng_web_search", "web_url_read"], "disabled": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "alwaysAllow": ["resolve-library-id", "get-library-docs"], "disabled": false}}}