# 标准化所有文本文件为 LF (Linux/Unix 风格)
# 这将确保 Git 在仓库中存储所有文本文件为 LF 换行符
* text=auto eol=lf

# 强制某些文件使用 LF 换行符 (例如 shell 脚本)
*.sh text eol=lf
*.py text eol=lf
*.js text eol=lf

# 强制某些文件使用 CRLF 换行符 (例如 Windows 特定脚本)
*.bat text eol=crlf
*.ps1 text eol=crlf

# 明确声明某些类型的文件为二进制，不进行任何换行符转换
*.png binary
*.jpg binary
*.jpg binary
*.jpeg binary
*.gif binary
*.zip binary
*.tar binary
*.gz binary
*.tgz binary
*.rar binary
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary
*.exe binary
*.dll binary
*.json binary
*.log binary