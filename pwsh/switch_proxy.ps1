# 代理配置
$defaultProxyHost = "127.0.0.1"
$defaultProxyPort = "7890"
$defaultProxyUrl = "http://${defaultProxyHost}:${defaultProxyPort}"

# 检查软件是否安装的函数
function Test-Software {
    param($name)
    if (-not (Get-Command $name -ErrorAction SilentlyContinue)) {
        Write-Host "❌ $name 未安装" -ForegroundColor Red
        return $false
    }
    return $true
}

# 获取代理 URL 的函数
function Get-ProxyUrl {
    $proxyHost = Read-Host "请输入代理主机地址 (默认: $defaultProxyHost)"
    $proxyPort = Read-Host "请输入代理端口 (默认: $defaultProxyPort)"

    if ([string]::IsNullOrWhiteSpace($proxyHost)) { $proxyHost = $defaultProxyHost }
    if ([string]::IsNullOrWhiteSpace($proxyPort)) { $proxyPort = $defaultProxyPort }

    return "http://${proxyHost}:${proxyPort}"
}

# 在现有代码中添加 VSCode 代理设置函数
function Set-VSCodeProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        # 获取 VSCode 设置文件路径
        $settingsPath = "$env:APPDATA\Code\User\settings.json"

        # 如果文件不存在，创建一个空的设置文件
        if (-not (Test-Path $settingsPath)) {
            New-Item -Path $settingsPath -ItemType File -Force | Out-Null
            Set-Content -Path $settingsPath -Value "{}" -Force
        }

        # 读取现有设置
        $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
        if ($null -eq $settings) {
            $settings = [PSCustomObject]@{}
        }

        if ($enable) {
            # 设置代理
            $settings | Add-Member -NotePropertyName "http.proxy" -NotePropertyValue $proxyUrl -Force
        } else {
            # 移除代理
            $settings.PSObject.Properties.Remove("http.proxy")
        }

        # 保存设置
        $settings | ConvertTo-Json -Depth 10 | Set-Content -Path $settingsPath -Force
        Write-Host "✅ VSCode 代理已$(if ($enable) { '设置' } else { '清除' })" -ForegroundColor Green
    } catch {
        Write-Host "❌ VSCode 代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 设置当前会话的代理环境变量
function Set-SessionProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        if ($enable) {
            # 设置当前会话的环境变量
            $env:http_proxy = $proxyUrl
            $env:https_proxy = $proxyUrl
            $env:all_proxy = $proxyUrl
            $env:no_proxy = 'localhost,127.0.0.1,localaddress,.localdomain.com,::1'

            Write-Host "✅ 当前会话代理已设置" -ForegroundColor Green
        } else {
            # 清除当前会话的环境变量
            Remove-Item Env:http_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:https_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:all_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:no_proxy -ErrorAction SilentlyContinue

            Write-Host "✅ 当前会话代理已清除" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ 当前会话代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 添加 Cursor 代理设置函数
function Set-CursorProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        # 获取 Cursor 设置文件路径
        $settingsPath = "$env:APPDATA\Cursor\User\settings.json"

        # 如果文件不存在，创建一个空的设置文件
        if (-not (Test-Path $settingsPath)) {
            New-Item -Path $settingsPath -ItemType File -Force | Out-Null
            Set-Content -Path $settingsPath -Value "{}" -Force
        }

        # 读取现有设置
        $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
        if ($null -eq $settings) {
            $settings = [PSCustomObject]@{}
        }

        if ($enable) {
            # 设置代理
            $settings | Add-Member -NotePropertyName "http.proxy" -NotePropertyValue $proxyUrl -Force
        } else {
            # 移除代理
            $settings.PSObject.Properties.Remove("http.proxy")
        }

        # 保存设置
        $settings | ConvertTo-Json -Depth 10 | Set-Content -Path $settingsPath -Force
        Write-Host "✅ Cursor 代理已$(if ($enable) { '设置' } else { '清除' })" -ForegroundColor Green
    } catch {
        Write-Host "❌ Cursor 代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 修改 proxyon 函数
function proxyon {
    param(
        [string]$proxyUrl = $null
    )

    if ([string]::IsNullOrWhiteSpace($proxyUrl)) {
        $proxyUrl = Get-ProxyUrl
    }

    Write-Host "`n正在配置代理服务... ($proxyUrl)" -ForegroundColor Cyan

    # 设置当前会话环境变量代理
    Set-SessionProxy -proxyUrl $proxyUrl -enable $true

    # 设置 Git 代理
    if (Test-Software "git") {
        try {
            git config --global http.proxy $proxyUrl | Out-Null
            git config --global https.proxy $proxyUrl | Out-Null
            Write-Host "✅ Git 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ Git 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 NPM 代理
    if (Test-Software "npm") {
        try {
            npm config set proxy $proxyUrl | Out-Null
            npm config set https-proxy $proxyUrl | Out-Null
            Write-Host "✅ NPM 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ NPM 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 Yarn 代理
    if (Test-Software "yarn") {
        try {
            yarn config set proxy $proxyUrl 2>&1 | Out-Null
            yarn config set https-proxy $proxyUrl 2>&1 | Out-Null
            Write-Host "✅ Yarn 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ Yarn 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 PNPM 代理
    if (Test-Software "pnpm") {
        try {
            pnpm config set proxy $proxyUrl | Out-Null
            pnpm config set https-proxy $proxyUrl | Out-Null
            Write-Host "✅ PNPM 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ PNPM 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 VSCode 代理
    Set-VSCodeProxy -proxyUrl $proxyUrl -enable $true

    # 设置 Cursor 代理
    Set-CursorProxy -proxyUrl $proxyUrl -enable $true

    Write-Host "`n✨ 代理服务已启动" -ForegroundColor Green
}

function proxyoff {
    Write-Host "`n正在关闭代理服务..." -ForegroundColor Cyan

    # 清除当前会话环境变量代理
    Set-SessionProxy -proxyUrl "" -enable $false

    # 清除 Git 代理
    if (Test-Software "git") {
        try {
            git config --global --unset http.proxy
            git config --global --unset https.proxy
            Write-Host "✅ Git 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ Git 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 NPM 代理
    if (Test-Software "npm") {
        try {
            npm config delete proxy | Out-Null
            npm config delete https-proxy | Out-Null
            Write-Host "✅ NPM 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ NPM 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 Yarn 代理
    if (Test-Software "yarn") {
        try {
            yarn config delete proxy 2>&1 | Out-Null
            yarn config delete https-proxy 2>&1 | Out-Null
            Write-Host "✅ Yarn 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ Yarn 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 PNPM 代理
    if (Test-Software "pnpm") {
        try {
            pnpm config delete proxy | Out-Null
            pnpm config delete https-proxy | Out-Null
            Write-Host "✅ PNPM 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ PNPM 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 VSCode 代理
    Set-VSCodeProxy -proxyUrl "" -enable $false

    # 清除 Cursor 代理
    Set-CursorProxy -proxyUrl "" -enable $false

    Write-Host "`n✨ 代理服务已关闭" -ForegroundColor Green
}

# 显示当前代理状态的函数
function proxystate {
    Write-Host "`n当前代理状态：" -ForegroundColor Cyan

    Write-Host "`n当前会话环境变量代理："
    Write-Host "HTTP_PROXY: $env:http_proxy"
    Write-Host "HTTPS_PROXY: $env:https_proxy"
    Write-Host "ALL_PROXY: $env:all_proxy"
    Write-Host "NO_PROXY: $env:no_proxy"

    if (Test-Software "git") {
        Write-Host "`nGit 代理："
        git config --global --get http.proxy
        git config --global --get https.proxy
    }

    if (Test-Software "npm") {
        Write-Host "`nNPM 代理："
        npm config get proxy
        npm config get https-proxy
    }

    if (Test-Software "yarn") {
        Write-Host "`nYarn 代理："
        yarn config get proxy
        yarn config get https-proxy
    }

    if (Test-Software "pnpm") {
        Write-Host "`nPNPM 代理："
        pnpm config get proxy
        pnpm config get https-proxy
    }

    # 添加 VSCode 代理状态显示
    try {
        $settingsPath = "$env:APPDATA\Code\User\settings.json"
        if (Test-Path $settingsPath) {
            $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
            Write-Host "`nVSCode 代理："
            Write-Host "HTTP_PROXY: $($settings.'http.proxy')"
        }
    } catch {
        Write-Host "❌ 无法读取 VSCode 设置: $($_.Exception.Message)" -ForegroundColor Red
    }

    # 添加 Cursor 代理状态显示
    try {
        $settingsPath = "$env:APPDATA\Cursor\User\settings.json"
        if (Test-Path $settingsPath) {
            $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
            Write-Host "`nCursor 代理："
            Write-Host "HTTP_PROXY: $($settings.'http.proxy')"
        }
    } catch {
        Write-Host "❌ 无法读取 Cursor 设置: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 弹出提示信息
Write-Host @"
代理工具使用说明：
- proxyon [proxyUrl]  : 启动代理（可选：指定代理URL）
- proxyoff           : 关闭代理
- proxystate         : 查看当前代理状态
"@ -ForegroundColor Yellow
