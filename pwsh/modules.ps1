# PowerShell 模块配置文件
# =============================================================================

# 安全导入模块函数
function Import-ModuleSafely {
    param(
        [string]$ModuleName,
        [string]$Description = ""
    )

    if (Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue) {
        Import-Module -Name $ModuleName -Force -ErrorAction SilentlyContinue
        if ($Description) {
            # 模块加载成功，静默模式
        }
    } else {
        Write-Warning "模块 '$ModuleName' 未安装。$Description"
        switch ($ModuleName) {
            "Terminal-Icons" { Write-Host "安装命令: Install-Module -Name Terminal-Icons -Scope CurrentUser" -ForegroundColor Yellow }
            "z" { Write-Host "安装命令: Install-Module -Name z -Scope CurrentUser" -ForegroundColor Yellow }
            "PSFzf" { Write-Host "安装命令: Install-Module -Name PSFzf -Scope CurrentUser" -ForegroundColor Yellow }
        }
    }
}

# =============================================================================
# 核心模块导入
# =============================================================================

# 导入Terminal-Icons模块（文件图标显示）
Import-ModuleSafely -ModuleName "Terminal-Icons" -Description "Terminal-Icons 已加载 (文件图标显示)"

# 设置 PSReadLine 选项（如果可用）
if (Get-Module -ListAvailable -Name PSReadLine -ErrorAction SilentlyContinue) {
    # 开启历史记录预测
    Set-PSReadLineOption -PredictionSource History -ErrorAction SilentlyContinue
    # 设置预测建议列表样式
    Set-PSReadLineOption -PredictionViewStyle ListView -ErrorAction SilentlyContinue
    # 设置编辑模式为 Windows 风格
    Set-PSReadLineOption -EditMode Windows -ErrorAction SilentlyContinue
    # PSReadLine 配置已应用
}

# =============================================================================
# Oh My Posh 主题设置
# =============================================================================

# 检查 Oh My Posh 是否可用
if (Get-Command oh-my-posh -ErrorAction SilentlyContinue) {
    # 检查主题文件是否存在
    $themeFile = "$env:POSH_THEMES_PATH/catppuccin.omp.json"
    if ($env:POSH_THEMES_PATH -and (Test-Path $themeFile -ErrorAction SilentlyContinue)) {
        oh-my-posh init pwsh --config $themeFile | Invoke-Expression
        # Oh My Posh 主题已加载
    } else {
        Write-Warning "Oh My Posh 主题文件未找到: $themeFile"
        Write-Host "请检查 POSH_THEMES_PATH 环境变量或主题文件是否存在" -ForegroundColor Yellow
    }
} else {
    Write-Warning "Oh My Posh 未安装。安装命令: winget install JanDeDobbeleer.OhMyPosh"
}

# =============================================================================
# 效率插件
# =============================================================================

# 目录快速跳转 (z)
Import-ModuleSafely -ModuleName "z" -Description "z 模块已加载 (目录快速跳转)"

# fzf 模糊搜索
if (Get-Command fzf -ErrorAction SilentlyContinue) {
    Import-ModuleSafely -ModuleName "PSFzf" -Description "PSFzf 模块已加载 (模糊搜索)"
} else {
    Write-Warning "fzf 未安装。请从 https://github.com/junegunn/fzf/releases 下载安装"
}
