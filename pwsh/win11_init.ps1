#Requires -RunAsAdministrator
<#
██╗    ██╗██╗███╗   ██╗ ██╗ ██╗    ██╗███╗   ██╗██╗████████╗
██║    ██║██║████╗  ██║ ██║ ██║    ██║████╗  ██║██║╚══██╔══╝
██║ █╗ ██║██║██╔██╗ ██║ ██║ ██║ █╗ ██║██╔██╗ ██║██║   ██║   
██║███╗██║██║██║╚██╗██║ ██║ ██║███╗██║██║╚██╗██║██║   ██║   
╚███╔███╔╝██║██║ ╚████║ ██║ ╚███╔███╔╝██║ ╚████║██║   ██║   
 ╚══╝╚══╝ ╚═╝╚═╝  ╚═══╝ ╚═╝  ╚══╝╚══╝ ╚═╝  ╚═══╝╚═╝   ╚═╝   

.SYNOPSIS
    Windows 11 开发环境自动化初始化脚本

.DESCRIPTION
    本脚本用于全新 Windows 11 系统的开发环境配置，自动安装和配置以下工具：
    - 包管理器：Scoop、Winget
    - 开发工具：Git、VS Code、Windows Terminal
    - 终端美化：Oh My Posh
    - 编程语言：Node.js (通过 NVM)、Python、Go
    - 包管理工具：npm、yarn、pnpm、pip

.NOTES
    作者: iBootz
    版本: 2.0
    日期: 2025-06-15
    特性: 支持国内镜像源、幂等性设计、美化终端
#>

# 出现错误时立即停止执行
$ErrorActionPreference = "Stop"

# --- 全局配置 ---
# 如果您需要通过代理服务器下载，请取消注释并设置
# $Global:ProxyUrl = "http://your-proxy-server:port" 

# 国内镜像源配置 (可根据需要修改)
$NpmMirror = "https://registry.npmmirror.com/"
$NvmMirror = "https://npmmirror.com/mirrors/nvm/"
$NodeMirror = "https://npmmirror.com/mirrors/node/"
$PipMirror = "https://pypi.tuna.tsinghua.edu.cn/simple"
$GoMirror = "https://goproxy.cn,direct"
# Python 相关配置
$PipMirror = "https://pypi.tuna.tsinghua.edu.cn/simple"  # PyPI 镜像
# 不再使用镜像安装 Python，直接使用官方源
# --- 全局配置结束 ---

# 如果设置了全局代理，则尝试为当前 PowerShell 会话设置环境变量
if ($Global:ProxyUrl) {
    Write-Host "检测到代理配置: $Global:ProxyUrl" -ForegroundColor Yellow
    $env:HTTP_PROXY = $Global:ProxyUrl
    $env:HTTPS_PROXY = $Global:ProxyUrl
    Write-Host "已为当前会话设置 HTTP_PROXY 和 HTTPS_PROXY 环境变量。" -ForegroundColor Green
}

# --- 输出函数 ---
# 使用符号和颜色增强输出的艺术性和可读性
function Write-StepHeader($text) {
    $border = "="*60
    Write-Host ""
    Write-Host $border -ForegroundColor Cyan
    Write-Host "✨ $text" -ForegroundColor Cyan
    Write-Host $border -ForegroundColor Cyan
}

function Write-Success($text) {
    Write-Host "✅ $text" -ForegroundColor Green
}

function Write-Warning($text) {
    Write-Host "⚠️ $text" -ForegroundColor Yellow
}

function Write-Error($text) {
    Write-Host "❌ $text" -ForegroundColor Red
}

function Write-Info($text) {
    Write-Host "ℹ️ $text" -ForegroundColor Blue
}

function Write-Step($text) {
    Write-Host "▶️ $text" -ForegroundColor Magenta
}

function Show-Progress($text) {
    # 使用沙漏图标表示进行中的任务，突出显示当前正在执行的步骤
    Write-Host "⏳ $text" -ForegroundColor Yellow
}

function Test-CommandExists($command) {
    return (Get-Command $command -ErrorAction SilentlyContinue) -ne $null
}

function Add-PathToProfile($path) {
    $profilePath = $PROFILE
    if (-not (Test-Path $profilePath)) {
        New-Item -Path $profilePath -ItemType File -Force | Out-Null
    }
    $content = Get-Content $profilePath
    if ($content -notcontains $path) {
        Add-Content -Path $profilePath -Value "`n`$env:PATH += ';$path'"
        Write-Success "已将 '$path' 添加到 PowerShell Profile。"
    }
}

# --- 前置条件检查 ---
function Check-Prerequisites() {
    Write-StepHeader "检查必要的前置条件"
    
    # 检查 winget 是否已安装
    if (-not (Test-CommandExists winget)) {
        Write-Error "未检测到 winget 命令。请先安装 'App Installer' (winget)，然后再运行此脚本。"
        Write-Host "您可以从 Microsoft Store 安装 'App Installer'：ms-windows-store://pdp/?ProductId=9NBLGGH4NNS1" -ForegroundColor Yellow
        exit 1
    }
    
    # 检查 scoop 是否已安装
    if (-not (Test-CommandExists scoop)) {
        Write-Error "未检测到 scoop 命令。请先安装 Scoop 包管理器，然后再运行此脚本。"
        Write-Host "您可以使用以下命令安装 Scoop：" -ForegroundColor Yellow
        Write-Host "Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force; irm get.scoop.sh | iex" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Success "前置条件检查通过：winget 和 scoop 均已安装。"
}

# --- 核心工具安装 ---
function Install-Git() {
    Write-StepHeader "安装 Git"
    if (Test-CommandExists git) {
        Write-Success "Git 已安装 ($(git --version))。"
        return
    }
    try {
        winget install --id Git.Git -e --source winget
        Write-Success "Git 安装成功。"
    } catch {
        Write-Error "使用 winget 安装 Git 失败: $_"
    }
}

function Install-VSCode() {
    Write-StepHeader "安装 Visual Studio Code"
    if (Test-CommandExists code) {
        Write-Success "Visual Studio Code 已安装。"
        return
    }
    try {
        winget install --id Microsoft.VisualStudioCode -e --source winget
        Write-Success "Visual Studio Code 安装成功。"
    } catch {
        Write-Error "使用 winget 安装 VSCode 失败: $_"
    }
}

function Install-NVM() {
    Write-StepHeader "安装 NVM for Windows 并配置镜像"
    
    # 更全面地检测 NVM 是否已安装
    $nvmDir = "$env:APPDATA\nvm"
    $nvmInstalled = $false
    
    # 检查方法1: 检查 NVM 可执行文件
    if (Test-Path "$nvmDir\nvm.exe") {
        $nvmInstalled = $true
    }
    
    # 检查方法2: 检查 NVM 命令是否可用
    if (Test-CommandExists nvm) {
        $nvmInstalled = $true
    }
    
    if ($nvmInstalled) {
        Write-Success "NVM for Windows 已安装，跳过安装步骤。"
    } else {
        try {
            Write-Host "正在安装 NVM for Windows..." -ForegroundColor Yellow
            winget install --id CoreyButler.NVMforWindows -e --source winget
            
            # 刷新环境变量以便检测新安装的 NVM
            $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
            
            Write-Success "NVM for Windows 安装成功。"
        } catch {
            Write-Error "使用 winget 安装 NVM for Windows 失败: $_"
            return
        }
    }
    
    # 配置 NVM 镜像
    try {
        # 确保 NVM 设置文件存在并配置镜像
        $settingsPath = "$nvmDir\settings.txt"
        if (-not (Test-Path $settingsPath)) {
            New-Item -Path $settingsPath -ItemType File -Force | Out-Null
            # 创建新文件时直接写入完整配置
            @"
node_mirror: $NodeMirror
npm_mirror: $NvmMirror
"@ | Out-File -FilePath $settingsPath -Encoding utf8
            Write-Success "已创建 NVM 配置文件并设置镜像。"
        } else {
            # 文件已存在，检查并添加缺失的配置
            $settings = Get-Content $settingsPath -Raw
            $updated = $false
            
            if ($settings -notmatch "node_mirror") { 
                Add-Content $settingsPath "`nnode_mirror: $NodeMirror" 
                $updated = $true
            }
            if ($settings -notmatch "npm_mirror") { 
                Add-Content $settingsPath "`nnpm_mirror: $NvmMirror" 
                $updated = $true
            }
            
            if ($updated) {
                Write-Success "已更新 NVM 配置文件中的镜像设置。"
            } else {
                Write-Success "NVM 镜像已正确配置。"
            }
        }
        
        # 确保 NVM 指向正确的安装目录
        if (Test-CommandExists nvm) {
            nvm root $nvmDir
            Write-Success "NVM 镜像配置完成。"
            
            # 检查是否已安装 Node.js
            $nodeInstalled = $false
            $nodeVersions = nvm list
            
            if ($nodeVersions -match "\d+\.\d+\.\d+") {
                $nodeInstalled = $true
                Write-Success "Node.js 已安装，跳过安装步骤。"
            } else {
                # 安装 Node.js LTS 版本
                Write-Host "正在安装 Node.js LTS 版本..." -ForegroundColor Yellow
                try {
                    # 使用确定的 LTS 版本号，完全避免使用 'lts' 或 'latest' 关键字
                    # 指定当前稳定的 LTS 版本，这里使用 v20.12.1
                    Write-Host "尝试安装 Node.js v20.12.1 (LTS)..." -ForegroundColor Yellow
                    nvm install 20.12.1
                    nvm use 20.12.1
                    Write-Success "Node.js v20.12.1 (LTS) 安装并激活成功。"
                } catch {
                    Write-Error "安装 Node.js v20.12.1 失败: $_"
                    Write-Host "尝试安装备选 LTS 版本..." -ForegroundColor Yellow
                    
                    # 备选 LTS 版本列表，按从新到旧的顺序
                    $ltsVersions = @("18.19.1", "16.20.2", "14.21.3")
                    $installed = $false
                    
                    foreach ($version in $ltsVersions) {
                        if (-not $installed) {
                            try {
                                Write-Host "尝试安装 Node.js v$version..." -ForegroundColor Yellow
                                nvm install $version
                                nvm use $version
                                Write-Success "Node.js v$version 安装并激活成功。"
                                $installed = $true
                                break
                            } catch {
                                Write-Warning "安装 Node.js v$version 失败，尝试下一个版本..."
                            }
                        }
                    }
                    
                    if (-not $installed) {
                        Write-Error "所有 Node.js 安装方式均失败。"
                        Write-Host "请访问 https://nodejs.org/en/download/ 手动安装 Node.js。" -ForegroundColor Yellow
                        return
                    }
                }
            }
        } else {
            Write-Warning "无法设置 NVM 根目录，请确保 NVM 已正确安装。"
            return
        }
        
        # 安装全局工具，使用 --force 参数解决文件已存在的问题
        Write-Host "正在安装 pnpm 和 yarn..." -ForegroundColor Yellow
        try {
            npm install -g pnpm yarn --force
            Write-Success "pnpm 和 yarn 安装完成。"
            
            # 验证安装
            if (Test-CommandExists node) {
                Write-Host "Node.js 版本: $(node -v)"
            }
            if (Test-CommandExists pnpm) {
                Write-Host "pnpm 版本: $(pnpm -v)"
            }
            if (Test-CommandExists yarn) {
                Write-Host "yarn 版本: $(yarn -v)"
            }
        } catch {
            Write-Warning "安装 pnpm 或 yarn 时出现问题: $_"
            Write-Host "这不会影响脚本的继续执行。" -ForegroundColor Yellow
        }
    } catch {
        Write-Error "NVM 配置或 Node.js 安装过程中出现错误: $_"
    }
}

function Install-Python() {
    Write-StepHeader "安装 uv 并使用它安装 Python"
    
    # 定义 uv 可能的安装路径
    $uvLocalPath = "$env:USERPROFILE\.local\bin\uv.exe"
    $uvCargoPath = "$env:USERPROFILE\.cargo\bin\uv.exe"
    
    if (-not (Test-CommandExists uv) -and -not (Test-Path $uvLocalPath) -and -not (Test-Path $uvCargoPath)) {
        try {
            Write-Host "正在安装 uv..."
            powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
            
            # 检查 uv 安装位置并添加到 PATH
            if (Test-Path $uvLocalPath) {
                $env:PATH += ";$env:USERPROFILE\.local\bin"
                Write-Host "已将 $env:USERPROFILE\.local\bin 添加到当前会话的 PATH" -ForegroundColor Yellow
            } elseif (Test-Path $uvCargoPath) {
                $env:PATH += ";$env:USERPROFILE\.cargo\bin"
                Write-Host "已将 $env:USERPROFILE\.cargo\bin 添加到当前会话的 PATH" -ForegroundColor Yellow
            }
            
            Write-Success "uv 安装成功。"
        } catch {
            Write-Error "uv 安装失败: $_"
            return
        }
    } else {
        # 如果 uv 已安装但不在 PATH 中，添加到 PATH
        if (-not (Test-CommandExists uv)) {
            if (Test-Path $uvLocalPath) {
                $env:PATH += ";$env:USERPROFILE\.local\bin"
                Write-Host "已将 $env:USERPROFILE\.local\bin 添加到当前会话的 PATH" -ForegroundColor Yellow
            } elseif (Test-Path $uvCargoPath) {
                $env:PATH += ";$env:USERPROFILE\.cargo\bin"
                Write-Host "已将 $env:USERPROFILE\.cargo\bin 添加到当前会话的 PATH" -ForegroundColor Yellow
            }
        }
        Write-Success "uv 已安装。"
    }
    
    # 确认 uv 可用
    if (-not (Test-CommandExists uv)) {
        # 尝试使用绝对路径
        if (Test-Path $uvLocalPath) {
            $uvCmd = $uvLocalPath
        } elseif (Test-Path $uvCargoPath) {
            $uvCmd = $uvCargoPath
        } else {
            Write-Error "无法找到 uv 可执行文件，Python 安装失败。"
            return
        }
    } else {
        $uvCmd = "uv"
    }
    
    try {
        # 检查是否已安装 Python
        $pythonInstalled = $false
        if (Test-CommandExists python) {
            $pythonVersion = python --version
            if ($pythonVersion -match "Python 3\.(\d+)") {
                $pythonInstalled = $true
                Write-Success "Python $pythonVersion 已安装，跳过安装步骤。"
            }
        }
        
        if (-not $pythonInstalled) {
            Write-Host "正在使用 uv 安装 Python 3.11..." -ForegroundColor Yellow
            
            # 使用确定的 uv 路径执行命令，不再使用镜像源
            try {
                # 尝试安装稳定的 3.11 版本，而不是最新的 3.12
                & $uvCmd python install 3.11
                & $uvCmd python pin 3.11
                
                # 将 uv 管理的 python shim 目录添加到 PATH
                Add-PathToProfile "$env:USERPROFILE\.local\bin" 
                $env:PATH += ";$env:USERPROFILE\.local\bin"
                
                Write-Success "Python 3.11 安装并固定成功。"
            } catch {
                Write-Error "uv 安装 Python 3.11 失败: $_"
                
                # 备选方案：使用 winget 安装 Python
                Write-Host "尝试使用 winget 安装 Python..." -ForegroundColor Yellow
                try {
                    winget install --id Python.Python.3.11 -e --source winget
                    Write-Success "Python 3.11 安装成功。"
                } catch {
                    Write-Error "使用 winget 安装 Python 失败: $_"
                    Write-Host "请访问 https://www.python.org/downloads/ 手动安装 Python。" -ForegroundColor Yellow
                    return
                }
            }
        }
        
        # 尝试使用 Python
        if (Test-CommandExists python) {
            $pythonVersion = python --version
            Write-Host "当前 Python 版本: $pythonVersion"
            
            # 配置 pip 镜像
            Write-Host "正在配置 pip 镜像..." -ForegroundColor Yellow
            try {
                $pipConfigDir = "$env:APPDATA\pip"
                if (-not (Test-Path $pipConfigDir)) {
                    New-Item -Path $pipConfigDir -ItemType Directory -Force | Out-Null
                }
                
                $pipConfigFile = "$pipConfigDir\pip.ini"
                $pipConfig = @"
[global]
index-url = $PipMirror
trusted-host = pypi.tuna.tsinghua.edu.cn
"@
                Set-Content -Path $pipConfigFile -Value $pipConfig -Encoding UTF8
                Write-Success "pip 镜像已配置为: $PipMirror"
            } catch {
                Write-Warning "pip 镜像配置失败: $_"
            }
        } else {
            Write-Warning "Python 已安装，但可能需要重启终端后才能使用。"
        }
    } catch {
        Write-Error "安装 Python 过程中出现错误: $_"
    }
}

function Install-Go() {
    Write-StepHeader "安装 Go 语言"
    if (Test-CommandExists go) {
        Write-Success "Go 已安装 ($(go version))。"
        return
    }
    try {
        scoop install go
        Write-Success "Go 安装成功。"
    } catch {
        Write-Error "使用 Scoop 安装 Go 失败: $_"
    }
}

function Install-OhMyPosh() {
    Write-StepHeader "安装 Oh My Posh 终端美化工具"
    
    # 检查是否已安装
    if (Test-CommandExists oh-my-posh) {
        $version = (oh-my-posh --version)
        Write-Success "Oh My Posh 已安装 (版本: $version)。"
        return
    }
    
    try {
        # 首选使用 winget 安装
        Write-Host "正在使用 winget 安装 Oh My Posh..." -ForegroundColor Yellow
        winget install JanDeDobbeleer.OhMyPosh -s winget
        
        # 刷新环境变量以便检测新安装的 oh-my-posh
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        if (Test-CommandExists oh-my-posh) {
            Write-Success "Oh My Posh 安装成功。"
            
            # 配置 PowerShell 配置文件以启用 Oh My Posh
            $profilePath = $PROFILE
            if (-not (Test-Path $profilePath)) {
                New-Item -Path $profilePath -ItemType File -Force | Out-Null
                Write-Host "已创建 PowerShell 配置文件: $profilePath" -ForegroundColor Green
            }
            
            # 检查是否已经配置了 Oh My Posh
            $profileContent = Get-Content $profilePath -Raw -ErrorAction SilentlyContinue
            if ($null -eq $profileContent -or $profileContent -notmatch "oh-my-posh") {
                # 添加 Oh My Posh 初始化代码到 PowerShell 配置文件
                $ohmyposhInit = @"

# Oh My Posh 终端美化配置
oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH\atomic.omp.json" | Invoke-Expression

"@
                Add-Content -Path $profilePath -Value $ohmyposhInit
                Write-Success "已将 Oh My Posh 配置添加到 PowerShell 配置文件。"
                Write-Host "提示: 你可以通过编辑 PowerShell 配置文件更改主题。" -ForegroundColor Yellow
                Write-Host "      可用主题列表: oh-my-posh get themes" -ForegroundColor Yellow
                Write-Host "      预览主题: oh-my-posh get themes | Out-Host -Paging" -ForegroundColor Yellow
            } else {
                Write-Host "Oh My Posh 已在 PowerShell 配置文件中配置。" -ForegroundColor Yellow
            }
            
            # 安装 Nerd 字体以支持图标显示
            Write-Host "`n你可能需要安装 Nerd 字体以正确显示 Oh My Posh 的所有图标:" -ForegroundColor Yellow
            Write-Host "https://www.nerdfonts.com/font-downloads" -ForegroundColor Cyan
            Write-Host "推荐安装: Cascadia Code、Hack、FiraCode 或 JetBrainsMono" -ForegroundColor Yellow
        } else {
            Write-Warning "Oh My Posh 安装可能成功，但无法在当前会话中找到命令。"
            Write-Warning "请在安装完成后重启 PowerShell，然后运行: oh-my-posh init pwsh | Invoke-Expression"
        }
    } catch {
        Write-Error "使用 winget 安装 Oh My Posh 失败: $_"
        
        # 尝试使用 Scoop 作为备选方案
        try {
            Write-Host "尝试使用 Scoop 安装 Oh My Posh..." -ForegroundColor Yellow
            scoop install oh-my-posh
            
            if (Test-CommandExists oh-my-posh) {
                Write-Success "使用 Scoop 安装 Oh My Posh 成功。"
                
                # 配置 PowerShell 配置文件
                $profilePath = $PROFILE
                if (-not (Test-Path $profilePath)) {
                    New-Item -Path $profilePath -ItemType File -Force | Out-Null
                }
                
                # 检查是否已经配置了 Oh My Posh
                $profileContent = Get-Content $profilePath -Raw -ErrorAction SilentlyContinue
                if ($null -eq $profileContent -or $profileContent -notmatch "oh-my-posh") {
                    # 添加 Oh My Posh 初始化代码到 PowerShell 配置文件
                    $ohmyposhInit = @"

# Oh My Posh 终端美化配置
oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH\atomic.omp.json" | Invoke-Expression

"@
                    Add-Content -Path $profilePath -Value $ohmyposhInit
                    Write-Success "已将 Oh My Posh 配置添加到 PowerShell 配置文件。"
                }
            } else {
                Write-Warning "Oh My Posh 安装可能成功，但无法在当前会话中找到命令。"
            }
        } catch {
            Write-Error "使用 Scoop 安装 Oh My Posh 也失败: $_"
            Write-Host "您可以尝试手动安装 Oh My Posh:" -ForegroundColor Yellow
            Write-Host "https://ohmyposh.dev/docs/installation/windows" -ForegroundColor Cyan
        }
    }
}

# --- 镜像源配置 ---
function Set-NpmMirror() {
    Write-StepHeader "配置 npm 镜像源"
    
    # 检查是否已安装 npm
    if (-not (Test-CommandExists npm)) {
        Write-Warning "npm 未安装，跳过镜像配置。"
        return
    }
    
    # 配置 npm 镜像
    try {
        npm config set registry $NpmMirror
        Write-Success "npm 镜像已设置为: $NpmMirror"
    } catch {
        Write-Warning "npm 镜像配置失败: $_"
    }
    
    # 检查并配置 pnpm
    if (Test-CommandExists pnpm) {
        try {
            pnpm config set registry $NpmMirror
            Write-Success "pnpm 镜像已设置为: $NpmMirror"
        } catch {
            Write-Warning "pnpm 镜像配置失败: $_"
        }
    } else {
        Write-Info "pnpm 未安装，跳过其镜像配置。"
    }
    
    # 检查并配置 yarn
    if (Test-CommandExists yarn) {
        try {
            yarn config set registry $NpmMirror
            Write-Success "yarn 镜像已设置为: $NpmMirror"
        } catch {
            Write-Warning "yarn 镜像配置失败: $_"
        }
    } else {
        Write-Info "yarn 未安装，跳过其镜像配置。"
    }
}

function Set-UvMirror() {
    Write-StepHeader "配置 uv (pip) 镜像源"
    try {
        $uvConfigDir = "$env:USERPROFILE\.config\uv"
        $uvConfigFile = "$uvConfigDir\uv.toml"
        if (-not (Test-Path $uvConfigDir)) {
            New-Item -Path $uvConfigDir -ItemType Directory -Force | Out-Null
        }
        # 使用 here-string 来创建配置文件内容
        $configContent = @"
# uv 全局配置文件
# 此文件用于为所有 uv 命令设置默认值
# 设置默认的 PyPI 索引镜像
index-url = "$PipMirror"
"@
        Set-Content -Path $uvConfigFile -Value $configContent -Encoding UTF8
        Write-Success "uv (pip) 镜像已在 '$uvConfigFile' 中设置为: $PipMirror"
    } catch {
        Write-Error "uv (pip) 镜像配置失败: $_"
    }
}

function Set-GoMirror() {
    Write-StepHeader "配置 Go 镜像源"
    try {
        [Environment]::SetEnvironmentVariable("GOPROXY", $GoMirror, "User")
        Write-Success "Go 镜像 GOPROXY 已设置为: $GoMirror"
    } catch {
        Write-Error "Go 镜像配置失败: $_"
    }
}

function Set-ScoopMirror() {
    Write-StepHeader "配置 Scoop 镜像源"
    try {
        scoop config SCOOP_REPO https://gitee.com/squall-wind/scoop-repo
        scoop config MAIN_BUCKET_REPO https://gitee.com/squall-wind/main-bucket
        Write-Success "Scoop 自身和主仓库已切换到 Gitee 镜像。"
    } catch {
        Write-Error "Scoop 镜像配置失败: $_"
    }
}

# --- 主流程 ---
# 使用艺术化的欢迎界面
function Show-WelcomeScreen {
    $welcomeBorder = "*"*80
    $emptyLine = "*" + " "*78 + "*"
    
    Clear-Host
    Write-Host $welcomeBorder -ForegroundColor Cyan
    Write-Host $emptyLine -ForegroundColor Cyan
    Write-Host ("*" + "{0,-78}" -f "" + "*") -ForegroundColor Cyan
    Write-Host ("*" + "{0,-78}" -f "                      欢迎使用 Windows 11 开发环境初始化工具                     " + "*") -ForegroundColor Cyan
    Write-Host ("*" + "{0,-78}" -f "                                 v2.0 by iBootz                                " + "*") -ForegroundColor Cyan
    Write-Host ("*" + "{0,-78}" -f "" + "*") -ForegroundColor Cyan
    Write-Host $emptyLine -ForegroundColor Cyan
    Write-Host $welcomeBorder -ForegroundColor Cyan
    Write-Host ""
    
    Write-Info "本脚本将在您的 Windows 11 系统上安装并配置以下开发工具："
    Write-Host "  ● 包管理器: " -NoNewline; Write-Host "Scoop, Winget" -ForegroundColor Yellow
    Write-Host "  ● 编程语言: " -NoNewline; Write-Host "Node.js (NVM), Python, Go" -ForegroundColor Yellow
    Write-Host "  ● 开发工具: " -NoNewline; Write-Host "Git, VS Code, Windows Terminal" -ForegroundColor Yellow
    Write-Host "  ● 终端美化: " -NoNewline; Write-Host "Oh My Posh" -ForegroundColor Yellow
    Write-Host "  ● 包管理器: " -NoNewline; Write-Host "npm, yarn, pnpm, pip" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Warning "按 Ctrl+C 可取消安装过程"
    Write-Host "按任意键开始安装..." -ForegroundColor Green
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Clear-Host
}

# 显示欢迎屏幕
Show-WelcomeScreen

# 移除重复的按键等待逻辑，因为已经在 Show-WelcomeScreen 函数中实现了等待

Write-Host "开始执行安装流程..." -ForegroundColor Green

# 检查前置条件
Show-Progress "检查系统前置条件"
Check-Prerequisites

# 首先设置 Scoop 镜像，以加速后续安装
Show-Progress "配置 Scoop 镜像源"
Write-Info "正在配置 Scoop 镜像源，这将显著加速后续安装过程"
Set-ScoopMirror

# 分组安装：开发工具
Show-Progress "安装 Git 版本控制工具"
Install-Git

Show-Progress "安装 Visual Studio Code 编辑器"
Install-VSCode
Install-Go         # Go 语言
Install-OhMyPosh   # 终端美化工具

# 配置其他镜像源
Write-Host "`n=== 配置其他镜像源以加速下载 ===`n" -ForegroundColor Cyan
Set-NpmMirror
Set-UvMirror
Set-GoMirror

# 刷新环境变量
Write-Host "`n=== 刷新当前会话的环境变量 ===`n" -ForegroundColor Cyan
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
# 对于新添加的 .local/bin 等路径，需要手动加入
if($env:PATH -notlike "*$env:USERPROFILE\.local\bin*") {
    $env:PATH += ";$env:USERPROFILE\.local\bin"
}


Write-Host "`n🎉 全部任务执行完毕！`n" -ForegroundColor Green
Write-Host "请注意：为了使所有环境变量 (如 Go 等) 完全生效，建议您`重启一个 PowerShell 终端`。"