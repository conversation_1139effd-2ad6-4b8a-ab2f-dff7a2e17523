# PowerShell 自定义函数配置文件
# =============================================================================

# 创建目录并立即进入
function mkcd {
    param([string]$Path)
    if ($Path) {
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
        Set-Location $Path
    } else {
        Write-Host "用法: mkcd <目录名>" -ForegroundColor Yellow
    }
}

# 查找进程
function psg {
    param([string]$Pattern)
    if ($Pattern) {
        Get-Process | Where-Object { $_.ProcessName -like "*$Pattern*" } | Select-Object -Property Name, Id, CPU, PM
    } else {
        Write-Host "用法: psg <进程名模式>" -ForegroundColor Yellow
    }
}

# 查找端口占用
function portuse {
    param([int]$Port)
    if ($Port) {
        Get-NetTCPConnection -LocalPort $Port -State Listen -ErrorAction SilentlyContinue | ForEach-Object {
            Get-Process -Id $_.OwningProcess -ErrorAction SilentlyContinue
        }
    } else {
        Write-Host "用法: portuse <端口号>" -ForegroundColor Yellow
    }
}

# 获取公网 IP
function myip {
    Invoke-RestMethod -Uri 'ifconfig.me/ip' -TimeoutSec 5 -ErrorAction SilentlyContinue
}

# 网络测试
function ping8 { Test-Connection ******* -ErrorAction SilentlyContinue }
