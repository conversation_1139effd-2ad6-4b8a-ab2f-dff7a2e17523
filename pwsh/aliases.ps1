# PowerShell 别名配置文件
# =============================================================================

# 安全别名设置函数
function Set-AliasSafely {
    param(
        [string]$Name,
        [string]$Value,
        [string]$Description = ""
    )

    if (Get-Alias -Name $Name -ErrorAction SilentlyContinue) {
        Remove-Alias -Name $Name -Force -ErrorAction SilentlyContinue
    }
    Set-Alias -Name $Name -Value $Value -Option AllScope -Description $Description -ErrorAction SilentlyContinue
}

# =============================================================================
# 文件系统 & 通用工具别名
# =============================================================================

# 安全的文件操作函数
function global:cp { Copy-Item @args -Confirm }
function global:mv { Move-Item @args -Confirm }
function global:rm { Remove-Item @args -Confirm -Recurse }

# 快速目录跳转
Set-AliasSafely -Name ".." -Value "Set-Location .."
Set-AliasSafely -Name "..." -Value "Set-Location ../.."
Set-AliasSafely -Name "...." -Value "Set-Location ../../.."

# 常用别名
Set-AliasSafely -Name "h" -Value "Get-History"
Set-AliasSafely -Name "grep" -Value "Select-String"

# =============================================================================
# Git 别名
# =============================================================================

if (Get-Command git -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "gs" -Value "git status"
    Set-AliasSafely -Name "ga" -Value "git add ."
    Set-AliasSafely -Name "gc" -Value "git commit -m"
    Set-AliasSafely -Name "gp" -Value "git push"
    Set-AliasSafely -Name "gpl" -Value "git pull"
    Set-AliasSafely -Name "gd" -Value "git diff"
    Set-AliasSafely -Name "gl" -Value "git log --oneline --decorate --all --graph"
    Set-AliasSafely -Name "gb" -Value "git branch"
    Set-AliasSafely -Name "gco" -Value "git checkout"
    Set-AliasSafely -Name "gst" -Value "git stash"
    Set-AliasSafely -Name "gsta" -Value "git stash apply"
    Set-AliasSafely -Name "gstp" -Value "git stash pop"
    Set-AliasSafely -Name "gcl" -Value "git clone"
}

# =============================================================================
# Docker 别名
# =============================================================================

if (Get-Command docker -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "dps" -Value "docker ps"
    Set-AliasSafely -Name "dpsa" -Value "docker ps -a"
    Set-AliasSafely -Name "dim" -Value "docker images"
    Set-AliasSafely -Name "dexec" -Value "docker exec -it"
    Set-AliasSafely -Name "dlogs" -Value "docker logs -f"
    Set-AliasSafely -Name "dcl" -Value "docker container prune"
    Set-AliasSafely -Name "diml" -Value "docker image prune"
    Set-AliasSafely -Name "dvl" -Value "docker volume prune"

    # Docker Compose 别名
    Set-AliasSafely -Name "dcu" -Value "docker compose up -d"
    Set-AliasSafely -Name "dcd" -Value "docker compose down"
    Set-AliasSafely -Name "dcr" -Value "docker compose restart"
    Set-AliasSafely -Name "dcb" -Value "docker compose build"
    Set-AliasSafely -Name "dcps" -Value "docker compose ps"
    Set-AliasSafely -Name "dclogs" -Value "docker compose logs -f"
}

# =============================================================================
# Node.js 别名
# =============================================================================

if (Get-Command npm -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "ni" -Value "npm install"
    Set-AliasSafely -Name "nr" -Value "npm run"
    Set-AliasSafely -Name "ns" -Value "npm start"
    Set-AliasSafely -Name "nb" -Value "npm run build"
    Set-AliasSafely -Name "nt" -Value "npm test"
}

if (Get-Command pnpm -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "pni" -Value "pnpm install"
    Set-AliasSafely -Name "pna" -Value "pnpm add"
    Set-AliasSafely -Name "pnr" -Value "pnpm run"
    Set-AliasSafely -Name "pns" -Value "pnpm start"
    Set-AliasSafely -Name "pnb" -Value "pnpm run build"
}

# =============================================================================
# Python 别名
# =============================================================================

if (Get-Command python -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "py" -Value "python"
}

if (Get-Command uv -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "uvi" -Value "uv install"
    Set-AliasSafely -Name "uvr" -Value "uv run"
    Set-AliasSafely -Name "uvs" -Value "uv sync"
}

# =============================================================================
# Kubernetes 别名
# =============================================================================

if (Get-Command kubectl -ErrorAction SilentlyContinue) {
    Set-AliasSafely -Name "k" -Value "kubectl"
    Set-AliasSafely -Name "kgp" -Value "kubectl get pods"
    Set-AliasSafely -Name "kgs" -Value "kubectl get svc"
    Set-AliasSafely -Name "kgd" -Value "kubectl get deploy"
    Set-AliasSafely -Name "klogs" -Value "kubectl logs -f"
    Set-AliasSafely -Name "kexec" -Value "kubectl exec -it"
}
