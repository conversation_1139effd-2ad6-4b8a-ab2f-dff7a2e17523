import requests
import streamlit as st


def main():
    st.title("大模型调试工具")

    base_url = st.text_input("Base URL", "")
    api_key = st.text_input("API Key", type="password")
    model_code = st.text_input("模型 Code (可选)", "")
    prompt = st.text_area("Prompt", "请输入您的提示内容")

    if not base_url or not api_key:
        st.error("请填写 Base URL 和 API Key")
        return

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": model_code or "default-model",
        "prompt": prompt,
        "max_tokens": 100
    }

    try:
        response = requests.post(f"{base_url}/v1/completions", headers=headers, json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        st.json(result)
    except requests.exceptions.RequestException as e:
        st.error(f"请求失败: {e}")

if __name__ == "__main__":
    main()
