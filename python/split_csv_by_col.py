import os
import csv
from typing import List, Dict
from multiprocessing import Pool, cpu_count

def process_csv_files(input_folder: str, output_folder: str, column_name: str) -> None:
    csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
    
    if len(csv_files) != len(set(csv_files)):
        raise ValueError("文件名重复，请确保所有CSV文件名唯一")
    
    with Pool(processes=cpu_count()) as pool:
        pool.starmap(process_single_file, 
                     [(input_folder, output_folder, csv_file, column_name) for csv_file in csv_files])

def process_single_file(input_folder: str, output_folder: str, csv_file: str, column_name: str) -> None:
    input_file = os.path.join(input_folder, csv_file)
    
    with open(input_file, mode='r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        header = [field.strip('"') for field in reader.fieldnames if field.strip()]
        
        print(f"处理文件: {csv_file}")
        print(f"列名: {header}")
        
        if column_name not in header:
            print(f"警告: 列名 '{column_name}' 在文件 {csv_file} 中不存在")
            print(f"可用的列名: {', '.join(header)}")
            return
        
        split_csv_by_val(input_file, output_folder, column_name, reader)

def split_csv_by_val(input_file: str, output_folder: str, column_name: str, reader: csv.DictReader) -> None:
    os.makedirs(output_folder, exist_ok=True)
    output_files = {}
    
    for row in reader:
        value = row[column_name]
        input_filename = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(output_folder, f"{input_filename}_{value}.csv")
        
        if output_file not in output_files:
            output_files[output_file] = True
            write_csv_row(output_file, row, reader.fieldnames, write_header=False)
        else:
            write_csv_row(output_file, row, reader.fieldnames, write_header=False)

def write_csv_row(output_file: str, row: Dict[str, str], fieldnames: List[str], write_header: bool = True) -> None:
    mode = 'w' if not os.path.exists(output_file) else 'a'
    
    with open(output_file, mode=mode, newline='', encoding='utf-8') as csvfile:
        valid_fieldnames = [f for f in fieldnames if f.strip()]
        writer = csv.DictWriter(csvfile, fieldnames=valid_fieldnames, quotechar='"', delimiter=',', quoting=csv.QUOTE_ALL)
        if write_header and mode == 'w':
            writer.writeheader()
        writer.writerow({k: v for k, v in row.items() if k in valid_fieldnames})

if __name__ == "__main__":
    input_folder = r'D:\Workspace\export\dmp_count'
    output_folder = r'D:\Workspace\export\dmp_count\output'
    column_name = 'month'
    process_csv_files(input_folder, output_folder, column_name)