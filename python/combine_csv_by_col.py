import os
import csv
from collections import defaultdict
import codecs

def detect_encoding(file_path):
    with open(file_path, 'rb') as f:
        raw = f.read(4)
    if raw.startswith(codecs.BOM_UTF8):
        return 'utf-8-sig'
    else:
        return 'utf-8'

def combine_csv_files(input_folder, output_folder, field_order=None):
    # 获取输入文件夹中的所有CSV文件
    csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
    
    # 用于存储合并后的数据
    combined_data = defaultdict(lambda: defaultdict(dict))
    all_columns = set()

    # 读取所有CSV文件并合并数据
    for file in csv_files:
        file_path = os.path.join(input_folder, file)
        encoding = detect_encoding(file_path)
        with open(file_path, 'r', encoding=encoding) as f:
            reader = csv.DictReader(f)
            for row in reader:
                month = row['month']
                org_id = row['org_id']
                for key, value in row.items():
                    if key and key not in ['month', 'org_id']:  # 添加 key 检查
                        combined_data[month][org_id][key] = value
                        all_columns.add(key)

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 写入合并后的数据到新的CSV文件
    for month in combined_data:
        output_file = os.path.join(output_folder, f"{month}.csv")
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            if field_order:
                fieldnames = ['month', 'org_id'] + [f for f in field_order if f]  # 过滤掉空字段
            else:
                fieldnames = ['month', 'org_id'] + sorted(list(all_columns))
            
            writer = csv.DictWriter(f, fieldnames=fieldnames, quoting=csv.QUOTE_ALL, delimiter=',')
            
            if not field_order:
                writer.writeheader()  # 只有在没有指定字段顺序时才写入表头
            
            for org_id, data in combined_data[month].items():
                row = {'month': month, 'org_id': org_id}
                row.update({k: v for k, v in data.items() if k in fieldnames})  # 只包含在 fieldnames 中的字段
                writer.writerow(row)

    print("文件合并完成。")

def main():
    input_folder = r'D:\Workspace\export\dmp_count'
    output_folder = r'D:\Workspace\export\dmp_count\combine'
    
    # 可选：指定字段顺序
    field_order = ['dmp_count', 'dmp_task_count', 'dmp_dim_count', 'dmp_user_count']  # 替换为您想要的字段顺序
    combine_csv_files(input_folder, output_folder, field_order)
    
    # 如果不指定字段顺序，直接调用
    # combine_csv_files(input_folder, output_folder)

if __name__ == "__main__":
    main()
