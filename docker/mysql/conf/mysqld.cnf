[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
pid-file	= /var/lib/mysql/mysqld.pid
socket		= /var/run/mysqld/mysqld.sock
datadir		= /var/lib/mysql
#log-error	= /var/log/mysql/error.log
# By default we only accept connections from localhost
bind-address	= 0.0.0.0
# symbolic-links is disabled by default in MySQL 8.4+

# 将mysql日志文件时间戳修改为跟随linux系统时区，解决日志时间跟东八区差8小时的问题
log_timestamps = SYSTEM

# 恢复为 MySQL 8.x 默认的严格模式，增强数据一致性
sql_mode=ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION

#
# 设置mysql支持utf8mb4编码和字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

#允许导入的sql文件大小, 设置为 1G
max_allowed_packet=1G

# 禁止MySQL对外部连接进行DNS解析，使用这一选项可以消除MySQL进行DNS解析的时间。但需要注意，如果开启该选项，
# 则所有远程主机连接授权都要使用IP地址方式，否则MySQL将无法正常处理连接请求
skip-name-resolve

# MySQL能有的连接数量。当主要MySQL线程在一个很短时间内得到非常多的连接请求，这就起作用，
# 然后主线程花些时间(尽管很短)检查连接并且启动一个新线程。back_log值指出在MySQL暂时停止回答新请求之前的短时间内多少个请求可以被存在堆栈中。
# 如果期望在一个短时间内有很多连接，你需要增加它。也就是说，如果MySQL的连接数据达到max_connections时，新来的请求将会被存在堆栈中，
# 以等待某一连接释放资源，该堆栈的数量即back_log，如果等待连接的数量超过back_log，将不被授予连接资源。
# 另外，这值（back_log）限于您的操作系统对到来的TCP/IP连接的侦听队列的大小。
# 你的操作系统在这个队列大小上有它自己的限制（可以检查你的OS文档找出这个变量的最大值），试图设定back_log高于你的操作系统的限制将是无效的。
back_log = 1000

#设置Mysql的最大连接数
# 根据服务器内存情况调整最大连接数
# 每个连接大约需要 4-10MB 内存
max_connections = 100

#防止暴力破解超过100次后禁止连接 成功连接一次后会清零
max_connect_errors = 100

#设置Mysql打开文件数
# 如果系统 ulimit -n 的值小于此值，则以此值为准
open_files_limit = 65535

# 设置表名不区分大小写
lower_case_table_names = 1

#未明确声明为NOT NULL的TIMESTAMP列允许NULL值。 将此列设置为NULL将其设置为NULL，而不是当前时间戳。
#没有TIMESTAMP列自动分配DEFAULT CURRENT_TIMESTAMP或ON UPDATE CURRENT_TIMESTAMP属性。 必须明确指定这些属性。
#声明为NOT NULL且没有显式DEFAULT子句的TIMESTAMP列被视为没有默认值。 对于不为此列指定显式值的插入行，结果取决于SQL模式。 如果启用了严格的SQL模式，则会发生错误。 如果未启用严格的SQL模式，则会为列分配隐式默认值“0000-00-00 00:00:00”，并发出警告。 这类似于MySQL如何处理其他时间类型，如DATETIME。
# explicit_defaults_for_timestamp # 此变量在 MySQL 8.x 中已废弃

#设置越大，在存取表的时候所需要的I/O越少&nbsp; 根据服务器情况设置
innodb_buffer_pool_size = 512M

# 0: log buffer每秒就会被刷写日志文件到磁盘，提交事务的时候不做任何操作（执行是由mysql的master thread线程来执行的。主线程中每秒会将重做日志缓冲写入磁盘的重做日志文件(REDO LOG)中。不论事务是否已经提交）默认的日志文件是ib_logfile0,ib_logfile1
#  
# 1: 当设为默认值1的时候，每次提交事务的时候，都会将log buffer刷写到日志。
# 2: 如果设为2,每次提交事务都会写日志，但并不会执行刷的操作。每秒定时会刷到日志文件。要注意的是，并不能保证100%每秒一定都会刷到磁盘，这要取决于进程的调度。
# 每次事务提交的时候将数据写入事务日志，而这里的写入仅是调用了文件系统的写入操作，而文件系统是有 缓存的，所以这个写入并不能保证数据已经写入到物理磁盘
# 默认值1是为了保证完整的ACID。当然，你可以将这个配置项设为1以外的值来换取更高的性能，但是在系统崩溃的时候，你将会丢失1秒的数据。
# 设为0的话，mysqld进程崩溃的时候，就会丢失最后1秒的事务。设为2,只有在操作系统崩溃或者断电的时候才会丢失最后1秒的数据。InnoDB在做恢复的时候会忽略这个值
#
# 总结
# 设为1当然是最安全的，但性能也是最差的（相对其他两个参数而言，但不是不能接受）。如果对数据一致性和完整性要求不高，完全可以设为2，如果只最求性能，例如高并发写的日志服务器，设为0来获得更高性能
innodb_flush_log_at_trx_commit = 1

# 此参数确定日志文件所用的内存大小，以M为单位。缓冲区更大能提高性能，但意外的故障将会丢失数据。
innodb_log_buffer_size = 8M

# 此参数确定数据日志文件的大小，更大的设置可以提高性能，但也会增加恢复故障数据库所需的时间
# 在 MySQL 8.0.30 之后，推荐使用 innodb_redo_log_capacity 替代 innodb_log_file_size 和 innodb_log_files_in_group
# 设置重做日志文件总大小为 2GB
innodb_redo_log_capacity = 2G

# 服务器关闭交互式连接前等待活动的秒数。交互式客户端定义为在mysql_real_connect()中使用CLIENT_INTERACTIVE选项的客户端。默认值：28800秒（8小时）
interactive_timeout = 7200

# 服务器关闭非交互连接之前等待活动的秒数。在线程启动时，根据全局wait_timeout值或全局interactive_timeout值初始化会话wait_timeout值，
# 取决于客户端类型(由mysql_real_connect()的连接选项CLIENT_INTERACTIVE定义)。参数默认值：28800秒（8小时）
# MySQL服务器所支持的最大连接数是有上限的，因为每个连接的建立都会消耗内存，因此我们希望客户端在连接到MySQL Server处理完相应的操作后，
# 应该断开连接并释放占用的内存。如果你的MySQL Server有大量的闲置连接，他们不仅会白白消耗内存，而且如果连接一直在累加而不断开，
# 最终肯定会达到MySQL Server的连接上限数，这会报'too many connections'的错误。对于wait_timeout的值设定，应该根据系统的运行情况来判断。
# 在系统运行一段时间后，可以通过show processlist命令查看当前系统的连接状态，如果发现有大量的sleep状态的连接进程，则说明该参数设置的过大，
# 可以进行适当的调整小些。要同时设置interactive_timeout和wait_timeout才会生效。
wait_timeout = 1800

# 跳过授权表 忘记密码root密码时添加此选项
# 注意：使用此选项后需要重启 MySQL 服务才能生效
#skip-grant-tables

# 启用性能模式
performance_schema = ON
