services:
  redis:
    container_name: searxng-redis
    image: docker.io/valkey/valkey:8-alpine
    command: valkey-server --save 30 1 --loglevel warning
    restart: unless-stopped
    networks:
      - searxng
    volumes:
      - './data/valkey-data:/data'
    user: "1000:1000"
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    logging:
      driver: "json-file"
      options:
        max-size: "1m"
        max-file: "1"

  searxng:
    container_name: searxng-service
    image: docker.io/searxng/searxng:latest
    restart: unless-stopped
    networks:
      - searxng
      - default
      - nginx-proxy
    ports:
      - "${SEARXNG_PORT:-9191}:8080"
    volumes:
      - './data/searxng:/etc/searxng:rw'
    environment:
      - SEARXNG_BASE_URL=${SEARXNG_BASE_URL:-http://localhost:9191/}
      - UWSGI_WORKERS=${SEARXNG_UWSGI_WORKERS:-2}
      - UWSGI_THREADS=${SEARXNG_UWSGI_THREADS:-4}
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    logging:
      driver: "json-file"
      options:
        max-size: "1m"
        max-file: "1"
    depends_on:
      - redis

networks:
  searxng:
    name: searxng-network
  default:
    name: searxng-default
  nginx-proxy:
    external: true
