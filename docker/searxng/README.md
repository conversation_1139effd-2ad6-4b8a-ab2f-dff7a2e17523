# SearXNG 服务

SearXNG 是一个免费的元搜索引擎，尊重隐私并且可以自托管。此目录包含用于部署 SearXNG 服务的 Docker Compose 配置。

## 管理脚本

我们提供了一个统一的管理脚本 `searxng-manager.sh`，可以实现所有 SearXNG 相关的功能：

```bash
# 显示帮助信息
./searxng-manager.sh help

# 安装和配置 SearXNG 服务
./searxng-manager.sh setup

# 配置 Nginx 反向代理和 SSL 证书
sudo ./searxng-manager.sh nginx

# 从 lobe-chat-db 迁移数据
./searxng-manager.sh migrate

# 启动 SearXNG 服务
./searxng-manager.sh start

# 停止 SearXNG 服务
./searxng-manager.sh stop

# 重启 SearXNG 服务
./searxng-manager.sh restart

# 查看 SearXNG 服务状态
./searxng-manager.sh status

# 查看 SearXNG 服务日志
./searxng-manager.sh logs

# 更新 SearXNG 服务
./searxng-manager.sh update
```

## 功能特点

- 支持多种搜索引擎（Google、Bing、DuckDuckGo、Baidu、Wikipedia 等）
- 提供 JSON 格式输出，方便与其他应用集成
- 包含 Redis 缓存，提高搜索性能
- 完全可定制的配置

## 快速开始

1. 确保已安装 Docker 和 Docker Compose

2. 克隆此仓库并进入 SearXNG 目录
   ```bash
   cd /path/to/scripts/docker/searxng
   ```

3. 设置脚本执行权限
   ```bash
   chmod +x searxng-manager.sh
   ```

4. 安装和配置 SearXNG 服务
   ```bash
   ./searxng-manager.sh setup
   ```

5. (可选) 配置 Nginx 反向代理和 SSL 证书，实现外部访问
   ```bash
   sudo ./searxng-manager.sh nginx
   ```

6. 访问 SearXNG 服务：
   - 本地访问：http://localhost:9191
   - 外部访问：https://search.ibootz.com (需要先运行步骤 5)

## 配置说明

可以通过修改 `.env` 文件来自定义 SearXNG 服务的配置：

```
# SearXNG 相关配置
# SearXNG 服务端口
SEARXNG_PORT=9191
# SearXNG 基础 URL，用于 SearXNG 服务本身
# 外部访问时使用 HTTPS URL
SEARXNG_BASE_URL=https://search.ibootz.com/
# SearXNG 工作进程数
SEARXNG_UWSGI_WORKERS=2
# SearXNG 线程数
SEARXNG_UWSGI_THREADS=4
```

## API 使用

SearXNG 提供了 JSON API 接口，可以用于集成到其他应用中。以下是一些使用示例：

### 基本搜索

```bash
curl "http://localhost:9191/search?q=搜索关键词&format=json"
```

### 指定搜索引擎

```bash
curl "http://localhost:9191/search?q=搜索关键词&engines=google,bing&format=json"
```

### 指定语言

```bash
curl "http://localhost:9191/search?q=搜索关键词&language=zh-CN&format=json"
```

### 指定搜索类型

```bash
curl "http://localhost:9191/search?q=搜索关键词&category=news&format=json"
```

可用的类型包括：general, images, videos, news, map, music, it, science, files


## 与 LobeChat 集成

要将 SearXNG 与 LobeChat 集成，请在 LobeChat 的 `.env` 文件中添加以下配置：

```
# SearXNG 相关配置
# SearXNG 基础 URL，用于 LobeChat 联网搜索功能
# 外部访问时使用 HTTPS URL
SEARXNG_URL=https://search.ibootz.com
```

## 外部访问配置

要从外部访问 SearXNG 服务，需要配置 Nginx 反向代理和 SSL 证书。

### 前提条件

1. 已安装并配置好 SearXNG 服务
2. 已安装 Nginx 服务器
3. 拥有一个可以解析到服务器 IP 的域名（本例中使用 `search.ibootz.com`）
4. 服务器的 80 和 443 端口可以从外部访问

### 使用管理脚本配置

使用统一管理脚本可以快速配置 Nginx 反向代理和 SSL 证书：

```bash
sudo ./searxng-manager.sh nginx
```

此命令会执行以下操作：

1. 检查并添加 SearXNG 服务到 Nginx 服务配置文件
2. 确保 SearXNG 服务已连接到 nginx-proxy 网络
3. 运行 Nginx 管理脚本设置反向代理
4. 为 `search.ibootz.com` 申请 SSL 证书

### 手动设置

如果您想手动设置，可以按照以下步骤操作：

#### 1. 添加 SearXNG 服务到 Nginx 服务配置文件

编辑 `/root/workspace/scripts/docker/_shell/rockylinux/nginx-services.conf` 文件，添加以下配置：

```
"search.ibootz.com|searxng-service|8080|true"
```

#### 2. 确保 SearXNG 服务已连接到 nginx-proxy 网络

```bash
# 检查 nginx-proxy 网络是否存在，如果不存在则创建
if ! docker network ls | grep -q "nginx-proxy"; then
  docker network create nginx-proxy
fi

# 重启 SearXNG 服务以应用网络配置
cd /root/workspace/scripts/docker/searxng
docker compose down
docker compose up -d
```

#### 3. 运行 Nginx 管理脚本设置反向代理

```bash
cd /root/workspace/scripts/docker/_shell/rockylinux
./nginx-manager.sh setup
```

#### 4. 申请 SSL 证书

```bash
cd /root/workspace/scripts/docker/_shell/rockylinux
./cert-manager.sh obtain search.ibootz.com
```

### 验证配置

配置完成后，您可以通过以下方式验证：

1. 本地访问：http://localhost:9191
2. 外部访问：https://search.ibootz.com

### 故障排除

如果遇到问题，请检查以下内容：

1. 确保域名 `search.ibootz.com` 已正确解析到服务器 IP
2. 确保服务器的 80 和 443 端口已开放
3. 检查 Nginx 和 SearXNG 服务的日志：
   ```bash
   # 检查 Nginx 日志
   tail -f /usr/local/nginx/logs/error.log
   
   # 检查 SearXNG 服务日志
   docker logs searxng-service
   ```

## 注意事项
- 为确保 LobeChat 能够正常使用 SearXNG 进行联网搜索，请确保 `SEARXNG_URL` 环境变量正确设置为 SearXNG 服务的访问地址。
- 如果看到 Redis 内存过度提交警告 (`vm.overcommit_memory must be enabled`)，可以在主机上运行以下命令解决：
  ```bash
  sudo sysctl vm.overcommit_memory=1
  ```
  要永久生效，请将 `vm.overcommit_memory = 1` 添加到 `/etc/sysctl.conf` 文件中。
