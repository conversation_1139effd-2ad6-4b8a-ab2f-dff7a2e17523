#!/bin/bash

# SearXNG 管理脚本
# 这是一个聚合入口脚本，统一实现所有 SearXNG 相关功能

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 脚本路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SETUP_SCRIPT="${SCRIPT_DIR}/setup.sh"
NGINX_PROXY_SCRIPT="${SCRIPT_DIR}/setup-nginx-proxy.sh"
MIGRATE_SCRIPT="${SCRIPT_DIR}/migrate-from-lobe-chat-db.sh"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SearXNG 管理脚本${NC}"
    echo -e "统一管理 SearXNG 服务的各项功能\n"
    echo -e "用法: $0 [命令] [选项]"
    echo -e "\n命令:"
    echo -e "  ${GREEN}setup${NC}         安装和配置 SearXNG 服务"
    echo -e "  ${GREEN}nginx${NC}         配置 Nginx 反向代理和 SSL 证书"
    echo -e "  ${GREEN}migrate${NC}       从 lobe-chat-db 迁移数据到独立的 SearXNG 服务"
    echo -e "  ${GREEN}start${NC}         启动 SearXNG 服务"
    echo -e "  ${GREEN}stop${NC}          停止 SearXNG 服务"
    echo -e "  ${GREEN}restart${NC}       重启 SearXNG 服务"
    echo -e "  ${GREEN}status${NC}        查看 SearXNG 服务状态"
    echo -e "  ${GREEN}logs${NC}          查看 SearXNG 服务日志"
    echo -e "  ${GREEN}update${NC}        更新 SearXNG 服务"
    echo -e "  ${GREEN}help${NC}          显示帮助信息"
    echo -e "\n示例:"
    echo -e "  $0 setup     # 安装和配置 SearXNG 服务"
    echo -e "  $0 nginx     # 配置 Nginx 反向代理和 SSL 证书"
    echo -e "  $0 logs      # 查看 SearXNG 服务日志"
}

# 检查脚本是否存在
check_script() {
    local script=$1
    local name=$2

    if [ ! -f "$script" ]; then
        echo -e "${RED}错误: $name 脚本不存在: $script${NC}"
        echo -e "请确保所有必要的脚本都存在于 ${SCRIPT_DIR} 目录中"
        exit 1
    fi
}

# 安装和配置 SearXNG 服务
setup() {
    echo -e "${GREEN}正在安装和配置 SearXNG 服务...${NC}"
    check_script "$SETUP_SCRIPT" "安装"

    # 执行安装脚本
    bash "$SETUP_SCRIPT"

    echo -e "\n${GREEN}SearXNG 服务安装和配置完成!${NC}"
    echo -e "您可以使用以下命令管理服务:"
    echo -e "  $0 start    # 启动服务"
    echo -e "  $0 stop     # 停止服务"
    echo -e "  $0 restart  # 重启服务"
    echo -e "  $0 nginx    # 配置 Nginx 反向代理和 SSL 证书"
}

# 配置 Nginx 反向代理和 SSL 证书
setup_nginx() {
    echo -e "${GREEN}正在配置 Nginx 反向代理和 SSL 证书...${NC}"
    check_script "$NGINX_PROXY_SCRIPT" "Nginx 配置"

    # 检查是否以 root 用户运行
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}错误: 配置 Nginx 需要 root 权限${NC}"
        echo -e "请使用 sudo 运行此命令:"
        echo -e "  sudo $0 nginx"
        exit 1
    fi

    # 检查 SearXNG 服务是否运行
    if ! docker ps | grep -q searxng-service; then
        echo -e "${YELLOW}警告: SearXNG 服务未运行，正在启动...${NC}"
        start
    fi

    # 执行 Nginx 配置脚本
    bash "$NGINX_PROXY_SCRIPT"

    echo -e "\n${GREEN}Nginx 反向代理和 SSL 证书配置完成!${NC}"
    echo -e "${GREEN}现在可以通过 https://search.ibootz.com 访问 SearXNG 服务${NC}"
    echo -e "${YELLOW}如果遇到 502 错误，请运行以下命令重新配置:${NC}"
    echo -e "  sudo $0 nginx"
}

# 从 lobe-chat-db 迁移数据
migrate() {
    echo -e "${GREEN}正在从 lobe-chat-db 迁移数据到独立的 SearXNG 服务...${NC}"

    if [ ! -f "$MIGRATE_SCRIPT" ]; then
        echo -e "${YELLOW}警告: 迁移脚本不存在: $MIGRATE_SCRIPT${NC}"
        echo -e "是否要创建迁移脚本? [y/N]"
        read -r create_script

        if [[ "$create_script" =~ ^[Yy]$ ]]; then
            echo -e "${GREEN}创建迁移脚本...${NC}"
            cat > "$MIGRATE_SCRIPT" << 'EOF'
#!/bin/bash

# 从 lobe-chat-db 迁移数据到独立的 SearXNG 服务

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 源目录和目标目录
SOURCE_DIR="/root/workspace/scripts/docker/lobe-chat-db/data"
TARGET_DIR="/root/workspace/scripts/docker/searxng/data"

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo -e "${RED}错误: 源目录不存在: $SOURCE_DIR${NC}"
    exit 1
fi

# 创建目标目录
mkdir -p "$TARGET_DIR"

# 检查是否有 SearXNG 数据需要迁移
if [ -d "$SOURCE_DIR/searxng" ]; then
    echo -e "${GREEN}迁移 SearXNG 数据...${NC}"
    mkdir -p "$TARGET_DIR/searxng"
    cp -r "$SOURCE_DIR/searxng"/* "$TARGET_DIR/searxng/"
    echo -e "${GREEN}SearXNG 数据迁移完成!${NC}"
else
    echo -e "${YELLOW}警告: 未找到 SearXNG 数据: $SOURCE_DIR/searxng${NC}"
fi

# 检查是否有 Redis 数据需要迁移
if [ -d "$SOURCE_DIR/redis-data" ]; then
    echo -e "${GREEN}迁移 Redis 数据...${NC}"
    mkdir -p "$TARGET_DIR/valkey-data"
    cp -r "$SOURCE_DIR/redis-data"/* "$TARGET_DIR/valkey-data/"
    echo -e "${GREEN}Redis 数据迁移完成!${NC}"
else
    echo -e "${YELLOW}警告: 未找到 Redis 数据: $SOURCE_DIR/redis-data${NC}"
fi

echo -e "${GREEN}数据迁移完成!${NC}"
EOF
            chmod +x "$MIGRATE_SCRIPT"
            echo -e "${GREEN}迁移脚本已创建: $MIGRATE_SCRIPT${NC}"
        else
            echo -e "${YELLOW}取消迁移操作${NC}"
            return
        fi
    fi

    # 执行迁移脚本
    bash "$MIGRATE_SCRIPT"

    echo -e "\n${GREEN}数据迁移完成!${NC}"
}

# 启动 SearXNG 服务
start() {
    echo -e "${GREEN}正在启动 SearXNG 服务...${NC}"
    cd "$SCRIPT_DIR" && docker compose up -d
    echo -e "${GREEN}SearXNG 服务已启动!${NC}"
    echo -e "可通过以下地址访问:"
    echo -e "- 本地访问: http://localhost:9191"
    echo -e "- 外部访问: https://search.ibootz.com (需要先配置 Nginx)"
}

# 停止 SearXNG 服务
stop() {
    echo -e "${GREEN}正在停止 SearXNG 服务...${NC}"
    cd "$SCRIPT_DIR" && docker compose down
    echo -e "${GREEN}SearXNG 服务已停止!${NC}"
}

# 重启 SearXNG 服务
restart() {
    echo -e "${GREEN}正在重启 SearXNG 服务...${NC}"
    cd "$SCRIPT_DIR" && docker compose restart
    echo -e "${GREEN}SearXNG 服务已重启!${NC}"
}

# 查看 SearXNG 服务状态
status() {
    echo -e "${GREEN}SearXNG 服务状态:${NC}"
    cd "$SCRIPT_DIR" && docker compose ps
}

# 查看 SearXNG 服务日志
logs() {
    local lines=${1:-100}
    echo -e "${GREEN}查看 SearXNG 服务日志 (最近 $lines 行):${NC}"
    cd "$SCRIPT_DIR" && docker compose logs --tail="$lines"
}

# 更新 SearXNG 服务
update() {
    echo -e "${GREEN}正在更新 SearXNG 服务...${NC}"
    cd "$SCRIPT_DIR" && docker compose pull
    echo -e "${GREEN}SearXNG 服务镜像已更新!${NC}"

    echo -e "${YELLOW}是否要重启服务以应用更新? [y/N]${NC}"
    read -r restart_service

    if [[ "$restart_service" =~ ^[Yy]$ ]]; then
        restart
    else
        echo -e "${YELLOW}服务未重启，更新将在下次重启时生效${NC}"
    fi
}

# 主函数
main() {
    local command=$1
    shift || true

    case "$command" in
        setup)
            setup "$@"
            ;;
        nginx)
            setup_nginx "$@"
            ;;
        migrate)
            migrate "$@"
            ;;
        start)
            start "$@"
            ;;
        stop)
            stop "$@"
            ;;
        restart)
            restart "$@"
            ;;
        status)
            status "$@"
            ;;
        logs)
            logs "$@"
            ;;
        update)
            update "$@"
            ;;
        help|--help|-h|"")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令: $command${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
