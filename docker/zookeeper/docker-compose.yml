version: '3.7'
services:
  zoo1:
    image: zookeeper
    container_name: zoo1
    restart: unless-stopped
    hostname: zoo1
    volumes:
      - $PWD/zoo1/logs:/logs
      - $PWD/zoo1/data:/data
      - $PWD/zoo1/datalog:/datalog
    networks:
      - zoo_net
    ports:
      - 2181:2181
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=0.0.0.0:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
      ZOO_LOG4J_PROP: 'INFO,ROLLINGFILE'

  zoo2:
    image: zookeeper
    container_name: zoo2
    restart: unless-stopped
    hostname: zoo2
    volumes:
      - $PWD/zoo2/logs:/logs
      - $PWD/zoo2/data:/data
      - $PWD/zoo2/datalog:/datalog
    networks:
      - zoo_net
    ports:
      - 2182:2181
    environment:
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=0.0.0.0:2888:3888;2181 server.3=zoo3:2888:3888;2181
      ZOO_LOG4J_PROP: 'INFO,R<PERSON><PERSON><PERSON><PERSON>LE'

  zoo3:
    image: zookeeper
    container_name: zoo3
    restart: unless-stopped
    hostname: zoo3
    volumes:
      - $PWD/zoo3/logs:/logs
      - $PWD/zoo3/data:/data
      - $PWD/zoo3/datalog:/datalog
    networks:
      - zoo_net
    ports:
      - 2183:2181
    environment:
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=0.0.0.0:2888:3888;2181
      ZOO_LOG4J_PROP: 'INFO,ROLLINGFILE'

networks:
  zoo_net:
    name: zoo_net

