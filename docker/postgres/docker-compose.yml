services:
  postgres:
    image: pgvector/pgvector:pg16
    container_name: postgres
    hostname: postgres
    restart: always
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-2048M}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - ./init:/docker-entrypoint-initdb.d
      - ./backup:/backup
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}
      PGDATA: /var/lib/postgresql/data/pgdata
      TZ: Asia/Shanghai
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - postgres_network
    logging:
      driver: json-file
      options:
        max-size: "100M"
        max-file: "10"

volumes:
  postgres_data:
    name: postgres_data

networks:
  postgres_network:
    driver: bridge

