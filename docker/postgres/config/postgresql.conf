# -----------------------------
# PostgreSQL 生产环境配置文件
# -----------------------------

# 连接设置
listen_addresses = '*'
max_connections = 100
superuser_reserved_connections = 3
unix_socket_directories = '/var/run/postgresql'

# 内存设置
shared_buffers = 1GB                  # 建议为系统内存的1/4
work_mem = 16MB                       # 复杂查询的内存
maintenance_work_mem = 256MB          # 维护操作的内存
effective_cache_size = 3GB            # 系统缓存估计值，建议为系统内存的1/2-3/4

# 写入设置
wal_level = replica                   # 允许备份和复制
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_timeout = 15min            # 检查点超时
checkpoint_completion_target = 0.9    # 检查点完成目标
random_page_cost = 1.1                # 对SSD存储优化
effective_io_concurrency = 200        # 对SSD存储优化

# 日志设置
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_truncate_on_rotation = off
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000     # 记录执行时间超过1秒的查询
log_checkpoints = on
log_connections = on
log_disconnections = on
log_line_prefix = '%m [%p] %q%u@%d '
log_statement = 'ddl'                 # 记录所有DDL语句
log_timezone = 'Asia/Shanghai'

# 统计信息
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
# stats_temp_directory = '/var/lib/postgresql/data/pg_stat_tmp'  # 在 PostgreSQL 15 中已移除

# 自动清理设置
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.05
autovacuum_analyze_scale_factor = 0.025

# 客户端连接设置
tcp_keepalives_idle = 60
tcp_keepalives_interval = 10
tcp_keepalives_count = 10

# 区域设置
datestyle = 'iso, mdy'
timezone = 'Asia/Shanghai'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'

# 安全设置
password_encryption = scram-sha-256    # 更安全的密码加密
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'

# 性能调优
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# 查询优化
default_statistics_target = 100
constraint_exclusion = partition
