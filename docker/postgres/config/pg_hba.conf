# PostgreSQL 客户端认证配置文件
# 格式为:
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# 本地连接
local   all             all                                     scram-sha-256
# IPv4 本地连接
host    all             all             127.0.0.1/32            scram-sha-256
# IPv6 本地连接
host    all             all             ::1/128                 scram-sha-256
# 允许容器网络内的连接
host    all             all             **********/12           scram-sha-256
# 允许特定网络的连接 (可根据实际环境调整)
host    all             all             10.0.0.0/8              scram-sha-256

# 复制连接 (用于备份和复制)
host    replication     all             127.0.0.1/32            scram-sha-256
host    replication     all             ::1/128                 scram-sha-256
host    replication     all             **********/12           scram-sha-256

# 如需允许外部网络访问，请取消下面的注释并修改为您的网络
host    all             all             0.0.0.0/0               scram-sha-256
