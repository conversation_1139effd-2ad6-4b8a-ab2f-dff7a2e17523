version: '3.8'

services:
  litellm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: litellm
    restart: unless-stopped
    ports:
      - "4000:4000"
    volumes:
      - ./config:/app/config
      - litellm_data:/app/data
    environment:
      - TZ=Asia/Shanghai
      - LITELLM_CONFIG=/app/config/config.yaml
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

volumes:
  litellm_data:
