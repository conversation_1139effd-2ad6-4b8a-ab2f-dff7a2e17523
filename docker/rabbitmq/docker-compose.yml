services:
  rabbitmq:
    image: heidiks/rabbitmq-delayed-message-exchange:3.13.3-management
    container_name: rabbitmq
    hostname: rabbitmq
    environment:
      - TZ=Asia/Shanghai
      - LANG=en_US.UTF-8
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=123456
      - RABBITMQ_ENABLED_PLUGINS_FILE=/etc/rabbitmq/enabled_plugins
    ports:
      - "15672:15672"
      - "5672:5672"
    volumes:
      - ./conf:/etc/rabbitmq
      - ./data:/var/lib/rabbitmq
    networks:
      - net_v1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_running"]
      interval: 10s
      timeout: 5s
      retries: 5
    sysctls:
      - net.core.somaxconn=1024
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000
networks:
  net_v1:
    external: true
