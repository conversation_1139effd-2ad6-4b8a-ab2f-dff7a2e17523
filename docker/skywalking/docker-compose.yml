version: '3.8'
services:
  oap:
    image: apache/skywalking-oap-server:9.3.0
    container_name: oap
    restart: unless-stopped
    networks:
      - net_v1
    ports:
      - 11800:11800
      - 12800:12800
    healthcheck:
      test: [ "CMD-SHELL", "/skywalking/bin/swctl ch" ]
      # 每间隔30秒执行一次
      interval: 30s
      # 健康检查命令运行超时时间，如果超过这个时间，本次健康检查就被视为失败；
      timeout: 10s
      # 当连续失败指定次数后，则将容器状态视为 unhealthy，默认 3 次。
      retries: 3
      # 应用的启动的初始化时间，在启动过程中的健康检查失效不会计入，默认 0 秒。
      start_period: 10s
    environment:
      TZ: Asia/Shanghai
      SW_STORAGE: mysql
      SW_JDBC_URL: "jdbc:mysql://**********:3306/skywalking?rewriteBatchedStatements=true&allowMultiQueries=true"
      SW_DATA_SOURCE_USER: skyw
      SW_DATA_SOURCE_PASSWORD: AGHAeiPyyr8mBizN
      SW_HEALTH_CHECKER: default
      SW_TELEMETRY: prometheus
      SW_ENABLE_UPDATE_UI_TEMPLATE: true
      JAVA_OPTS: "-Xms512m -Xmx512m"
    volumes:
      - $PWD/mysql-connector-java-8.0.30.jar:/skywalking/oap-libs/mysql-connector-java-8.0.30.jar
  ui:
    image: apache/skywalking-ui:9.3.0
    container_name: ui
    depends_on:
      oap:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - net_v1
    ports:
      - 28080:8080
    environment:
      TZ: Asia/Shanghai
      SW_OAP_ADDRESS: http://oap:12800

networks:
  net_v1:
    external: true
