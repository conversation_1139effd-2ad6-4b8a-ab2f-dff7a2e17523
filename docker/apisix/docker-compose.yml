version: "3"

services:
  apisix-dashboard:
    image: apache/apisix-dashboard:2.10.1-alpine
    restart: always
    volumes:
    - ./dashboard_conf/conf.yaml:/usr/local/apisix-dashboard/conf/conf.yaml
    ports:
    - "9000:9000"
    networks:
      net_v1:

  apisix:
    image: apache/apisix:2.12.1-alpine
    restart: always
    volumes:
      - ./apisix_log:/usr/local/apisix/logs
      - ./apisix_conf/config.yaml:/usr/local/apisix/conf/config.yaml:ro
    depends_on:
      - etcd
    ##network_mode: host
    ports:
      - "80:80/tcp"
      - "9091:9091/tcp"
      - "443:443/tcp"
      - "9092:9092/tcp"
    networks:
      net_v1:

  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - $PWD/../data/apisix/etcd:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379/tcp"
    networks:
      net_v1:

networks:
  net_v1:
    driver: bridge
    external: true
