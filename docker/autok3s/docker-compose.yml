version: '3.7'
services:
    autok3s:
        image: cnrancher/autok3s:v0.5.2
        container_name: autok3s
        hostname: autok3s
        ports:
            - "28880:8080"
        networks:
            - net_v1
        environment:
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options:
            max-size: "100M"
            max-file: "10"
networks:
    net_v1:
        external: true
