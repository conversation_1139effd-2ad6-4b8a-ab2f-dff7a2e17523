services:
  jenkins:
    image: jenkins/jenkins:lts-jdk21
    container_name: jenkins
    restart: unless-stopped
    privileged: true
    user: root
    ports:
      - "8181:8080"
      - "50000:50000"
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Duser.timezone=Asia/Shanghai
      - JENKINS_OPTS=--httpPort=8080
      - JENKINS_OPTS=--sessionTimeout=43200
      - JENKINS_OPTS=--sessionEviction=86400
    volumes:
      - ./data:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/login"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 备份服务
  backup:
    image: alpine
    container_name: jenkins-backup
    volumes:
      - ./data:/var/jenkins_home
      - ./backup:/backup
    command: >
      sh -c "while true; do
        sleep 86400 && \
        tar czf /backup/jenkins-backup-$$(date +%Y%m%d).tar.gz -C /var/jenkins_home . && \
        find /backup -name 'jenkins-backup-*.tar.gz' -mtime +7 -delete
      done"
    restart: always

networks:
  default:
    driver: bridge
