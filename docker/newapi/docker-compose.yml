services:
  new-api:
    image: calciumion/new-api:latest
    container_name: new-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    env_file:
      - ./.env
    environment:
      - SQL_DSN=${RDS_USER}:${RDS_PASSWORD}@tcp(${RDS_HOST}:${RDS_PORT})/${RDS_DATABASE}
      - REDIS_CONN_STRING=redis://${REDIS_HOST:-redis}:${REDIS_PORT:-6379}
      - TZ=${TZ:-Asia/Shanghai}
    #      - SESSION_SECRET=random_string  # 多机部署时设置，必须修改这个随机字符串！！！！！！！
    #      - NODE_TYPE=slave  # 多机部署的从节点取消注释
    #      - SYNC_FREQUENCY=60  # 如需定期同步数据库，取消注释
    #      - FRONTEND_BASE_URL=https://your-domain.com  # 多机部署带前端URL时取消注释

    depends_on:
      - redis
      # - mysql
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:latest
    container_name: redis
    restart: always

  # mysql:
  #   image: mysql:8.2
  #   container_name: mysql
  #   restart: always
  #   environment:
  #     MYSQL_ROOT_PASSWORD: 123456  # 确保与SQL_DSN中的密码一致
  #     MYSQL_DATABASE: new-api
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   # ports:
    #   - "3306:3306"  # 如需从Docker外部访问MySQL，取消注释

# volumes:
#   mysql_data:
