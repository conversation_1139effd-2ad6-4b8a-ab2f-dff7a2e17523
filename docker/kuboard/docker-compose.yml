version: '3.7'
services: 
    Kuboard: 
        image: swr.cn-east-2.myhuaweicloud.com/kuboard/kuboard:v3
        container_name: kuboard
        hostname: kuboard
        deploy:
            resources:
                limits:
                    memory: 1024M
        ports:
            - "18880:80"
            - "10081:10081"
        volumes:
          - ~/docker-compose/data/kuboard:/data
        networks:
            - net_v1
        environment:
          KUBOARD_ADMIN_DERAULT_PASSWORD: "123456"
          KUBOARD_ENDPOINT: "http://***************:18880"
          KUBOARD_AGENT_SERVER_TCP_PORT: "10081"
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options: 
            max-size: "100M"
            max-file: "10"
networks:
    net_v1:
        external: true
    
