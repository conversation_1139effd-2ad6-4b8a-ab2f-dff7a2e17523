# 必填，LobeChat 域名，用于 tRPC 调用
# 请保证此域名在你的 NextAuth 鉴权服务提供商、S3 服务商的 CORS 白名单中
APP_URL=https://example.com/

# Postgres 相关，也即 DB 必需的环境变量
# 必填，用于加密敏感信息的密钥，可以使用 openssl rand -base64 32 生成
KEY_VAULTS_SECRET=xxxxxx
# 必填，Postgres 数据库连接字符串，用于连接到数据库
# 格式：postgresql://username:password@host:port/dbname，如果你的 pg 实例为 Docker 容器且位于同一 docker-compose 文件中，亦可使用容器名作为 host
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/lobe
# 必填，数据库连接池大小
DATABASE_POOL_SIZE=20

# NEXT_AUTH 相关，也即鉴权服务必需的环境变量
# 可以使用 auth0、Azure AD、GitHub、Authentik、Zitadel、Logto 等，如有其他接入诉求欢迎提 PR
# 目前支持的鉴权服务提供商请参考：https://lobehub.com/zh/docs/self-hosting/advanced/auth#next-auth
# 如果你有 ACCESS_CODE，请务必清空，我们以 NEXT_AUTH 作为唯一鉴权来源
# 必填，用于 NextAuth 的密钥，可以使用 openssl rand -base64 32 生成
NEXT_AUTH_SECRET=xxxxxx
# 必填，指定鉴权服务提供商，这里以 Logto 为例
NEXT_AUTH_SSO_PROVIDERS=logto
# 必填，NextAuth 的 URL，用于 NextAuth 的回调
NEXTAUTH_URL=https://example.com/api/auth

# NextAuth 鉴权服务提供商部分，以 Logto 为例
# 其他鉴权服务提供商所需的环境变量，请参考：https://lobehub.com/zh/docs/self-hosting/environment-variables/auth
AUTH_LOGTO_ID=xxxxxx
AUTH_LOGTO_SECRET=xxxxxx
AUTH_LOGTO_ISSUER=https://xxx.logto.app/oidc

# 代理相关，如果你需要的话（比如你使用 GitHub 作为鉴权服务提供商）
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890

# S3 相关，也即非结构化数据（文件、图片等）存储必需的环境变量
# 这里以 MinIO 为例
# 必填，S3 的 Access Key ID，对于 MinIO 来说，直到在 MinIO UI 中手动创建之前都是无效的
S3_ACCESS_KEY_ID=xxxxxx
# 必填，S3 的 Secret Access Key，对于 MinIO 来说，直到在 MinIO UI 中手动创建之前都是无效的
S3_SECRET_ACCESS_KEY=xxxxxx
# 必填，S3 的 Endpoint，用于服务端/客户端连接到 S3 API
S3_REGION=xxxxxx
S3_ENDPOINT=xxxxxx
# 必填，S3 的 Bucket，直到在 MinIO UI 中手动创建之前都是无效的
S3_BUCKET=ibootz-lobe
# 必填，S3 的 Public Domain，用于客户端通过公开连接访问非结构化数据
S3_PUBLIC_DOMAIN=xxxxxx
# 选填，S3 的 Enable Path Style
# 对于主流 S3 Cloud 服务商，一般填 0 即可；对于自部署的 MinIO，请填 1
# 请参考：https://lobehub.com/zh/docs/self-hosting/advanced/s3#s-3-enable-path-style
S3_ENABLE_PATH_STYLE=0

# 其他基础环境变量，视需求而定。注意不要有 ACCESS_CODE
# 请参考：https://lobehub.com/zh/docs/self-hosting/environment-variables/basic
# 请注意，对于服务端版本，其 API 必须支持嵌入（即 OpenAI text-embedding-3-small）模型，否则无法对上传文件进行处理，但你无需在 OPENAI_MODEL_LIST 中指定此模型
OPENAI_API_KEY=xxxxxx
OPENAI_PROXY_URL=xxxxxx


#全局 API 速率限制（除中继请求外），单 ip 三分钟内的最大请求数，默认为 180
GLOBAL_API_RATE_LIMIT=1800
#全局 Web 速率限制，单 ip 三分钟内的最大请求数，默认为 60
GLOBAL_WEB_RATE_LIMIT=600

# SearXNG 相关配置
# SearXNG 基础 URL，用于 LobeChat 联网搜索功能
# 注意：这里使用的是独立部署的 SearXNG 服务
# 外部访问时使用 HTTPS URL
SEARXNG_URL=http://127.0.0.1:9191
