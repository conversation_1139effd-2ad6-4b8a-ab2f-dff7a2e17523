services:
  # SearXNG 和 Redis 服务已移至独立的 docker-compose 文件
  # 请参考 /root/workspace/scripts/docker/searxng/docker-compose.yml

  # postgresql:
  #   image: pgvector/pgvector:pg16
  #   container_name: lobe-postgres
  #   ports:
  #     - '5432:5432'
  #   volumes:
  #     - './data/postgres:/var/lib/postgresql/data'
  #   environment:
  #     - 'POSTGRES_DB=lobe'
  #     - 'POSTGRES_PASSWORD=uWNZugjBqixf8dxC'
  #     - 'PG_MAX_CONNECTIONS=50'
  #   command: >
  #     postgres
  #     -c max_connections=${PG_MAX_CONNECTIONS:-50}
  #     -c shared_buffers=${PG_SHARED_BUFFERS:-256MB}
  #     -c effective_cache_size=${PG_EFFECTIVE_CACHE_SIZE:-768MB}
  #     -c maintenance_work_mem=${PG_MAINTENANCE_WORK_MEM:-64MB}
  #     -c checkpoint_completion_target=${PG_CHECKPOINT_COMPLETION_TARGET:-0.9}
  #     -c wal_buffers=${PG_WAL_BUFFERS:-8MB}
  #     -c default_statistics_target=${PG_DEFAULT_STATISTICS_TARGET:-100}
  #     -c random_page_cost=${PG_RANDOM_PAGE_COST:-1.1}
  #     -c effective_io_concurrency=${PG_EFFECTIVE_IO_CONCURRENCY:-100}
  #     -c work_mem=${PG_WORK_MEM:-2MB}
  #     -c min_wal_size=${PG_MIN_WAL_SIZE:-512MB}
  #     -c max_wal_size=${PG_MAX_WAL_SIZE:-2GB}
  #   healthcheck:
  #     test: ['CMD-SHELL', 'pg_isready -U postgres']
  #     interval: 5s
  #     timeout: 5s
  #     retries: 5
  #   restart: always

  # minio:
  #   image: minio/minio
  #   container_name: lobe-minio
  #   ports:
  #     - '9000:9000'  # MinIO API
  #     - '9001:9001'  # MinIO Console
  #   volumes:
  #     - './data/minio_data:/etc/minio/data'
  #   environment:
  #     - 'MINIO_ROOT_USER=admin'
  #     - 'MINIO_ROOT_PASSWORD=l0e43b@uYZk3'
  #     - 'MINIO_DOMAIN=minio-api.ibootz.com'
  #     - 'MINIO_BROWSER_REDIRECT_URL=https://minio-console.ibootz.com'
  #     - 'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.ibootz.com'
  #   restart: always
  #   command: server /etc/minio/data --address ":9000" --console-address ":9001"

  # 开源版bug百出，改为使用logto cloud
  # logto:
  #   image: svhd/logto
  #   container_name: lobe-logto
  #   ports:
  #     - '3001:3001'  # Logto API
  #     - '3002:3002'  # Logto Admin UI
  #   depends_on:
  #     postgresql:
  #       condition: service_healthy
  #   environment:
  #     - 'TRUST_PROXY_HEADER=1'
  #     - 'DB_URL=******************************************************/logto'
  #     - 'ENDPOINT=https://logto-api.ibootz.com'
  #     - 'ADMIN_ENDPOINT=https://logto-console.ibootz.com'
  #     - 'PORT=3001'
  #     - 'ADMIN_PORT=3002'
  #     - 'HTTP_PROXY=http://*************:7890'
  #     - 'HTTPS_PROXY=http://*************:7890'
  #   entrypoint: ['sh', '-c', 'npm run cli db seed -- --swe && npm start']
  #   extra_hosts:
  #     - "lobe.ibootz.com:*************"
  #     - "minio-api.ibootz.com:*************"
  #     - "minio-console.ibootz.com:*************"
  #     - "logto-console.ibootz.com:*************"
  #     - "logto-api.ibootz.com:*************"

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat-db
    network_mode: "host"
    # depends_on:
      # - postgresql
      # - minio
      # - logto
      # - searxng
    env_file:
      - .env
    restart: always

networks:
  searxng:
    external: true
    name: searxng-network
