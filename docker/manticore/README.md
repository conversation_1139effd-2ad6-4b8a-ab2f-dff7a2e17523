# Manticore Search 容器化部署

本目录包含使用 Docker Compose 部署 Manticore Search 的配置文件。

## 服务说明

Manticore Search 是一个开源的全文搜索引擎，与 MySQL 协议兼容，可作为 Elasticsearch 的替代方案。

## 快速开始

1. 确保已安装 Docker 和 Docker Compose
2. 创建外部网络（如果尚未创建）：
   ```bash
   docker network create net_v1
   ```
3. 启动服务：
   ```bash
   docker-compose up -d
   ```

## 服务配置

- **镜像版本**：`manticoresearch/manticore:latest`
- **数据卷**：`./data` 映射到容器内的 `/var/lib/manticore`
- **时区**：`Asia/Shanghai`
- **资源限制**：
  - 进程数：65535
  - 文件描述符：65535
  - 内存锁定：无限制

## 端口说明

- **9306**：MySQL 协议端口
- **9308**：HTTP 协议端口

## 数据持久化

所有数据都保存在 `./data` 目录下，即使容器被删除，数据也不会丢失。

## 日志配置

- 日志驱动：json-file
- 单个日志文件最大：100MB
- 保留日志文件数：10

## 常用命令

- 启动服务：`docker-compose up -d`
- 停止服务：`docker-compose down`
- 查看日志：`docker-compose logs -f`
- 进入容器：`docker exec -it manticore bash`

## 注意事项

1. 确保宿主机端口 9306 和 9308 未被占用
2. 首次启动可能需要一些时间初始化数据
3. 建议在生产环境中配置适当的内存限制和监控

## 参考文档

- [Manticore Search 官方文档](https://manual.manticoresearch.com/)
- [Docker Hub - Manticore Search](https://hub.docker.com/r/manticoresearch/manticore/)
