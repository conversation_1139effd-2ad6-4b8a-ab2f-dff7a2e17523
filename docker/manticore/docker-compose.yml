services:
    manticore:
        image: manticoresearch/manticore
        container_name: manticore
        environment:
            - EXTRA=1
            - TZ: Asia/Shanghai
        restart: unless-stopped
        ports:
            - "9306:9306"
            - "9308:9308"
        ulimits:
            nproc: 65535
            nofile:
                soft: 65535
                hard: 65535
            memlock:
                soft: -1
                hard: -1
        volumes:
            - ./data:/var/lib/manticore
        networks:
            - net_v1
        logging:
            driver: json-file
            options:
                max-size: "100M"
                max-file: "10"

networks:
    net_v1:
        external: true
