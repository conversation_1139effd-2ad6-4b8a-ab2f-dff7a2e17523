version: '3.7'

services:
  etcd:
    image: ibootz/coreos-etcd:v3.4.1
    hostname: etcd
    networks:
      - dbnet
    ports:
      - '2379:2379'
      - '2380:2380'
      - '4001:4001'
    environment:
      ETCD_NAME: etcd0
      ETCD_DATA_DIR: /etcd-data
      ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379,http://0.0.0.0:4001
      ETCD_ADVERTISE_CLIENT_URLS: http://etcd:2379,http://etcd:4001
      ETCD_LISTEN_PEER_URLS: http://0.0.0.0:2380
      ETCD_INITIAL_ADVERTISE_PEER_URLS: http://etcd:2380
      ETCD-INITIAL-CLUSTER: etcd0=http://etcd:2380
      ETCD_INITIAL_CLUSTER_STATE: new
      ETCD_INITIAL_CLUSTER_TOKEN: etcd-cluster-1
      ETCD_ENABLE_V2: 'true'
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
  db:
    image: perconalab/percona-xtradb-cluster:latest
    networks: 
      - dbnet
    ports:
      - '3306:3306'
    volumes:
      - 'db_data:/var/lib/mysql'
    environment:
      DISCOVERY_SERVICE: 'etcd:2379'
      MYSQL_ROOT_PASSWORD: 123456
      XTRABACKUP_PASSWORD: 123456
      CLUSTER_NAME: db-cluster
    deploy: 
      mode: global
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s  

networks:
  dbnet:
    driver: overlay
    name: dbnet
    ipam:
      driver: default
      config:
        - subnet: *********/24

volumes:
  db_data:
