# pxc-docker-swarm-57
Publish a pxc cluster using the docker swarm mode and the docker-compose.yml.  
etcd:v3.4.1 + pxc:5.7 + docker swarm(by docker-compose.yml)  
  
Three swarm nodes:  
vm130:manager  
vm131:worker  
vm132:worker  

The etcd is deployed in the manager node, and the pxc is deployed by global mode.

You can deploy them in two ways: 
  1. docker service cerate ... ..., just using pxc-init.sh.
  2. docker stack deploy -c docker-compose.yml pxc.
