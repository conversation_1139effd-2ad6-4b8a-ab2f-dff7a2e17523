#!/bin/bash
# auto-update-lobe-chat.sh

# 加载shell全局环境变量
source /etc/profile

# Set proxy (optional)
export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

# Check if FORCE_UPDATE variable is set
FORCE_UPDATE=${1:-}

# Pull the latest image and store the output in a variable
output=$(docker pull lobehub/lobe-chat:latest 2>&1)

# Check if the pull command was executed successfully
if [ $? -ne 0 ]; then
  echo "拉取镜像失败!!!"
  exit 1
fi

# Check if the output contains a specific string
echo "$output" | grep -q "Image is up to date for lobehub/lobe-chat:latest"

# If the image is already up to date and FORCE_UPDATE is not set, do nothing
if [ $? -eq 0 ] && [ -z "$FORCE_UPDATE" ]; then
  echo "已是最新版本!!!"
  exit 0
fi

echo "Detected Lobe-Chat update"

# Remove the old container
echo "Removed: $(docker rm -f Lobe-Chat)"

# You may need to navigate to the directory where `docker-compose.yml` is located first
# cd /path/to/docker-compose-folder

# Run the new container
echo "Start Up... $(docker compose -f /root/docker-compose/lobe-chat/docker-compose.yml up -d)"

# Print the update time and version
echo "Update Time: $(date '+%Y-%m-%d %H:%M:%S')"
echo "Version: $(docker inspect lobehub/lobe-chat:latest | grep 'org.opencontainers.image.version' | awk -F'"' '{print $4}')"

# Clean up unused images
docker images | grep 'lobehub/lobe-chat' | grep -v 'latest' | awk '{print $3}' | xargs -r docker rmi > /dev/null 2>&1
echo "Removed old images."


# Tag the latest image with a new tag
echo "Push tag to aliyun"
docker tag lobehub/lobe-chat:latest registry.cn-hangzhou.aliyuncs.com/ibootz/lobe-chat:latest

# Push the newly tagged image to the registry
docker push registry.cn-hangzhou.aliyuncs.com/ibootz/lobe-chat:latest
