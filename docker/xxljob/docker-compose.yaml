version: '3.7'
services: 
    xxljob: 
        image: xuxueli/xxl-job-admin:2.4.0
        container_name: xxljob
        hostname: xxljob
        deploy:
            resources:
                limits:
                    memory: 1024M
        ports:
            - "18070:8080"
        volumes:
            - $PWD/../data/xxljob/:/data/applogs
        networks:
            - net_v1
        environment:
          DB_HOST: mysql8
          DB_USERNAME: root
          DB_PASSWORD: 123456
          PARAMS: |
            --server.servlet.context-path=/
            --spring.datasource.url=jdbc:mysql://$${DB_HOST}:3306/$${DB_NAME:xxl_job}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
            --spring.datasource.username=$${DB_USERNAME}
            --spring.datasource.password=$${DB_PASSWORD}
            --spring.datasource.hikari.minimum-idle=5
            --spring.datasource.hikari.maximum-pool-size=30
            --spring.mail.host=smtp.qq.com
            --spring.mail.port=25
            --spring.mail.username=<EMAIL>
            --spring.mail.from=<EMAIL>
            --spring.mail.password=xxx
            --xxl.job.accessToken=default_token
            --xxl.job.triggerpool.fast.max=200
            --xxl.job.triggerpool.slow.max=100
          JAVA_OPTS: '-Xmx512m'
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options: 
            max-size: "100M"
            max-file: "10"
networks:
    net_v1:
        external: true
    
