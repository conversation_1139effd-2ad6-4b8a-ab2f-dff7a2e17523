spring:
  cloud:
    nacos:
      config:
        # if use Nacos config set this flag to true
        enabled: true #use Nacos Config? (default:false)
        # need replace
        server-addr: nacos1:8848 #please input the nacos config server-addr (default:localhost:8848)
        namespace: public #please input the nacos config type (default:null)
        group: DEFAULT_GROUP #please input the nacos config group (default:DEFAULT_GROUP)
        prefix: fizz-gateway #please input the nacos config data-id (default:application)
        extension-configs[0]:
          refresh: true
          data-id: ${spring.cloud.nacos.config.prefix}
          group: ${spring.cloud.nacos.config.group}
