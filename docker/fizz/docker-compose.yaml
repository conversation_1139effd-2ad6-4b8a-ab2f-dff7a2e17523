version: '3.7'
services:
    fizz-manager: 
        image: fizzgate/fizz-manager-professional:2.5.2
        container_name: fizz-manager
        hostname: fizz-manager
        deploy:
            resources:
                limits:
                    cpus: '2'
                    memory: '1G'
        ports:
            - "8000:8000"
        volumes:
            - $PWD/fizz-manager/config:/opt/fizz-manager-professional/config
            - $PWD/fizz-manager/logs:/opt/fizz-manager-professional/logs
        networks:
            - net_v1
        environment:
          SPRING_PROFILES_ACTIVE: dev
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options: 
            max-size: "200M"
            max-file: "10"

    fizz-gateway: 
        image: fizzgate/fizz-gateway-community:2.5.2
        container_name: fizz-gateway
        hostname: fizz-gateway
        deploy:
            resources:
                limits:
                    cpus: '2'
                    memory: '1G'
        ports:
            - "8600:8600"
        volumes:
            - $PWD/fizz-gateway/config:/opt/fizz-gateway-community/config
            - $PWD/fizz-gateway/logs:/opt/fizz-gateway-community/logs
        networks:
            - net_v1
        environment:
          SPRING_PROFILES_ACTIVE: dev
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options: 
            max-size: "200M"
            max-file: "10"
networks:
    net_v1:
        external: true
    
