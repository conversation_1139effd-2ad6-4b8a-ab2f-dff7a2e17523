#!/bin/bash

# 安装必要的依赖
sudo yum install -y curl

# 下载 Clash
CLASH_VERSION="v1.15.0"
curl -L -o clash.tar.gz "https://github.com/Dreamacro/clash/releases/download/${CLASH_VERSION}/clash-linux-amd64-${CLASH_VERSION}.gz"

# 解压文件
tar -xzvf clash.tar.gz

# 移动到可执行目录
sudo mv clash-linux-amd64-${CLASH_VERSION} /usr/local/bin/clash

# 创建配置目录
sudo mkdir -p /etc/clash

# 下载示例配置文件
curl -L -o /etc/clash/config.yaml "https://example.com/clash_config.yaml"  # 请将此处的链接替换为您的实际配置文件链接

# 启动 Clash
sudo nohup /usr/local/bin/clash -d /etc/clash > /dev/null 2>&1 &

# 设置系统代理
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 使代理设置生效
sudo sed -i '/^.*http_proxy.*$/export http_proxy="http://127.0.0.1:7890"/' /etc/profile
sudo sed -i '/^.*https_proxy.*$/export https_proxy="http://127.0.0.1:7890"/' /etc/profile

source /etc/profile
