version: "3.7"
services:
  gitlab:
    image: registry.gitlab.cn/omnibus/gitlab-jh:latest
    container_name: gitlab
    hostname: 'gitlab.ibootz.com'
    ports:
      - '48080:80'
      - '48443:443'
      - '45050:5050'
      - '42222:22'
    environment:
      TZ: 'Asia/Shanghai'
      GITLAB_ROOT_PASSWORD: 'XIAOqiang@2021'
      GITLAB_OMNIBUS_CONFIG: |
        external_url 'https://gitlab.ibootz.com'
        gitlab_rails['time_zone'] = 'Asia/Shanghai'
        nginx['listen_port'] = '80'
        nginx['listen_https'] = false
        nginx['redirect_http_to_https'] = false
        nginx['ssl_certificate'] = '/etc/gitlab/ssl/ibootz.com.crt'
        nginx['ssl_certificate_key'] = '/etc/gitlab/ssl/ibootz.com.key'
        nginx['proxy_set_headers'] = {
          "Host" => "$$http_host_with_default",
          "X-Real-IP" => "$$remote_addr",
          "X-Forwarded-For" => "$$proxy_add_x_forwarded_for",
          "X-Forwarded-Proto" => "http",
          "Upgrade" => "$$http_upgrade",
          "Connection" => "$$connection_upgrade"
        }
        
        #开启ssh端口:这里配置的是外网穿透ssh端口
        gitlab_rails['gitlab_shell_ssh_port'] = 6002

        #ssl 采用手动配置证书的方式
        letsencrypt['enable'] = false

        gitlab_workhorse['image_scaler_max_filesize'] = 2500000

        # 电子邮件相关功能
        gitlab_rails['smtp_enable'] = true
        # 开启之后似乎会导致不断重启mail_room的问题
        #gitlab_rails['incoming_email_enabled'] = true
        gitlab_rails['gitlab_email_enabled'] = true
        gitlab_rails['smtp_tls'] = true
        gitlab_rails['smtp_address'] = "smtp.163.com"
        gitlab_rails['smtp_port'] = 465
        gitlab_rails['smtp_user_name'] = "<EMAIL>"
        gitlab_rails['smtp_password'] = "BQKMUCJRGPVGQIAK"
        gitlab_rails['smtp_domain'] = "163.com"
        gitlab_rails['smtp_authentication'] = "login"
        gitlab_rails['smtp_enable_starttls_auto'] = true
        gitlab_rails['gitlab_email_from'] = "<EMAIL>"
        gitlab_rails['gitlab_email_display_name'] = 'Gitlab'
        gitlab_rails['gitlab_email_reply_to'] = "<EMAIL>"

        # CI
        gitlab_ci['gitlab_ci_all_broken_builds'] = true
        gitlab_ci['********************'] = true

        # Terraform
        gitlab_rails['terraform_state_enabled'] = true

        # 打开容器仓库功能
        gitlab_rails['gitlab_default_projects_features_container_registry'] = true
        gitlab_rails['registry_enabled'] = true
        registry['enable'] = true
        registry_external_url 'https://registry.ibootz.com'
        registry_nginx['enable'] = true
        registry_nginx['listen_port'] = '5050'
        registry_nginx['listen_https'] = false
        registry_nginx['redirect_http_to_https'] = false
        registry_nginx['ssl_certificate'] = '/etc/gitlab/ssl/ibootz.com.crt'
        registry_nginx['ssl_certificate_key'] = '/etc/gitlab/ssl/ibootz.com.key'
        nginx['proxy_set_headers'] = {
          "Host" => "$$http_host_with_default",
          "X-Real-IP" => "$$remote_addr",
          "X-Forwarded-For" => "$$proxy_add_x_forwarded_for",
          "X-Forwarded-Proto" => "http",
          "Upgrade" => "$$http_upgrade",
          "Connection" => "$$connection_upgrade"
        }

        # 包仓库
        gitlab_rails['packages_enabled'] = true
        gitlab_rails['dependency_proxy_enabled'] = true

        # GitLab KAS
        gitlab_kas['enable'] = true
        gitlab_rails['gitlab_kas_enabled'] = true
        gitlab_kas['gitlab_address'] = 'http://gitlab.ibootz.com'
        gitlab_rails['gitlab_kas_external_url'] = 'ws://***************:48080/-/kubernetes-agent/'
        gitlab_rails['gitlab_kas_external_k8s_proxy_url'] = 'https://***************:48443/-/kubernetes-agent/'

        # Mattermost
        mattermost['enable'] = false
        mattermost_nginx['enable'] = false

        # Kerberos
        gitlab_rails['kerberos_enabled'] = false
        sentinel['enable'] = false

        # GitLab Pages
        gitlab_pages['enable'] = false
        pages_nginx['enable'] = false

        # 禁用 PUMA 集群模式
        puma['worker_processes'] = 2
        puma['min_threads'] = 4
        puma['max_threads'] = 4
        
        # 降低后台守护进程并发数
        sidekiq['max_concurrency'] = 4

        # Usage Statistics
        gitlab_rails['usage_ping_enabled'] = false
        gitlab_rails['sentry_enabled'] = false
        grafana['reporting_enabled'] = false

        # 关闭监控
        prometheus['enable'] = false
        prometheus_monitoring['enable'] = false
        alertmanager['enable'] = false
        node_exporter['enable'] = false
        redis_exporter['enable'] = false
        postgres_exporter['enable'] = false
        pgbouncer_exporter['enable'] = false
        gitlab_exporter['enable'] = false
        grafana['enable'] = false
        sidekiq['metrics_enabled'] = false
    networks:
      - net_v1
    restart: always
    volumes:
      - '~/docker-compose/data/gitlab/data:/var/opt/gitlab'
      - '~/docker-compose/data/gitlab/logs:/var/log/gitlab'
      - '~/docker-compose/gitlab/conf:/etc/gitlab'
networks:
    net_v1:
        external: true
