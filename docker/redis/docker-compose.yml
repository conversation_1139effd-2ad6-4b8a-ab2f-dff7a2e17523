services:
  redis:
    image: redis:6
    container_name: redis
    hostname: redis
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
      - "16379:16379"
    volumes:
      - "./data:/data"
    restart: unless-stopped
    # Note: vm.overcommit_memory needs to be set on the host system
    # Run this on the host: sudo sysctl vm.overcommit_memory=1
    # To make it persistent, add 'vm.overcommit_memory = 1' to /etc/sysctl.conf
    sysctls:
      net.core.somaxconn: 1024
    networks:
      - net_v1
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --appendonly yes
networks:
  net_v1:
    external: true
