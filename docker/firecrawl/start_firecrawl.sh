#!/bin/bash

# 设置脚本在任何命令失败时退出
set -e

# 定义项目仓库地址
REPO_URL="https://github.com/mendableai/firecrawl.git"
# 定义克隆的目标目录名
PROJECT_DIR="firecrawl"
# 定义.env文件的相对路径 (相对于脚本所在的目录 docker/firecrawl)
# 当脚本执行时，如果脚本本身在 docker/firecrawl/start_firecrawl.sh
# 并且 .env 也在 docker/firecrawl/.env
# 那么在脚本开始执行时，ENV_FILE_SOURCE 应该是 ".env"
# 当 cd $PROJECT_DIR 后，源 .env 路径变为 "../.env"
ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR=".env"


echo "开始执行 Firecrawl 启动/更新脚本..."

# 检查是否已存在项目目录
if [ -d "$PROJECT_DIR" ]; then
  echo "检测到目录 '$PROJECT_DIR' 已存在于 '$(pwd)/$PROJECT_DIR'。"
  echo "进入项目目录并拉取最新代码..."
  cd $PROJECT_DIR
  git pull origin main # 拉取最新代码，这里假设主分支是 main
  echo "已拉取最新代码。"
else
  echo "目录 '$PROJECT_DIR' 不存在，开始克隆项目从 $REPO_URL 到目录 $PROJECT_DIR..."
  git clone $REPO_URL $PROJECT_DIR
  echo "进入项目目录 '$PROJECT_DIR'..."
  cd $PROJECT_DIR
fi

echo "3. 获取并切换到最新的 git tag..."
# 获取最新的tag名称
latest_tag=$(git describe --tags `git rev-list --tags --max-count=1`)
if [ -z "$latest_tag" ]; then
  echo "错误：无法获取最新的 tag。"
  cd .. # 返回到脚本初始目录
  # 如果是新克隆的，可以考虑删除目录
  # if [ ! -d "$PROJECT_DIR" ]; then rm -rf "$PROJECT_DIR"; fi
  exit 1
fi
echo "最新的 tag 是: $latest_tag"
git checkout $latest_tag
echo "已切换到 tag: $latest_tag"

echo "4. 复制 .env 文件..."
# 此时脚本的执行目录是 docker/firecrawl/firecrawl
# .env 文件位于 docker/firecrawl/.env
# 因此，源文件路径是 ../.env (相对于当前 firecrawl 目录)
# ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR 指的是相对于脚本最初执行目录的 .env 文件
# 所以这里应该是 "../$ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR"
if [ -f "../$ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR" ]; then
  cp "../$ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR" ./.env
  echo ".env 文件已成功复制到项目根目录。"
else
  echo "错误: 源 .env 文件 '../$ENV_FILE_SOURCE_RELATIVE_TO_SCRIPT_DIR' 未找到。"
  cd .. # 返回到脚本初始目录
  # rm -rf "$PROJECT_DIR" # 可选：是否清理
  exit 1
fi

echo "5. 停止现有的 docker compose 服务 (忽略错误)..."
docker compose down || true

echo "6. 启动 docker compose 服务..."
# 假设 docker-compose.yaml 文件在项目根目录
if [ -f "docker-compose.yaml" ]; then
  docker compose up -d
  echo "Docker Compose 服务已在后台启动 (使用 docker-compose.yaml)。"
elif [ -f "docker-compose.yml" ]; then
  docker compose -f docker-compose.yml up -d
  echo "Docker Compose 服务已在后台启动 (使用 docker-compose.yml)。"
else
  echo "错误: 未在项目根目录找到 docker-compose.yaml 或 docker-compose.yml 文件。"
  # cd .. # 返回到脚本初始目录
  # rm -rf "$PROJECT_DIR" # 可选：是否清理
  exit 1
fi

# 检查 Docker 登录状态
echo "6. 检查 Docker 登录状态..."
if docker info 2>&1 | grep Username > /dev/null; then
  echo "Docker 已登录，继续打包和上传镜像。"
else
  echo "警告：Docker 未登录。请先运行 'docker login' 进行登录。"
  # 返回到脚本初始目录（如果需要，但这里不退出，只是警告并跳过上传）
  # cd ..
  exit 1 # 根据用户要求，如果未登录则结束脚本
fi

echo "7. 检查远程仓库是否已存在同名同标签的镜像..."
# 尝试拉取镜像，如果成功则说明镜像已存在
if docker pull ibootz/firecrawl:$latest_tag > /dev/null 2>&1 && docker pull ibootz/playwright-service-ts:$latest_tag > /dev/null 2>&1; then
  echo "远程仓库已存在标签为 '$latest_tag' 的镜像，跳过打包和上传。"
else
  echo "远程仓库不存在标签为 '$latest_tag' 的镜像，开始打包和上传。"

  echo "8. 为 Docker 镜像打标签..."
  # 使用获取到的 latest_tag 为镜像打标签
  # 假设本地构建的镜像名称是 firecrawl-api 和 firecrawl-playwright-service
  docker tag firecrawl-api:latest ibootz/firecrawl:$latest_tag
  docker tag firecrawl-playwright-service:latest ibootz/playwright-service-ts:$latest_tag
  echo "已为镜像 firecrawl-api 和 firecrawl-playwright-service 打上标签 $latest_tag。"

  echo "9. 上传 Docker 镜像到远程仓库..."
  docker push ibootz/firecrawl:$latest_tag
  docker push ibootz/playwright-service-ts:$latest_tag
  echo "Docker 镜像 ibootz/firecrawl:$latest_tag 和 ibootz/playwright-service-ts:$latest_tag 已上传。"
fi

echo "Firecrawl 启动/更新/打包/上传脚本执行完毕。项目目录位于 '$(pwd)'"
echo "要停止服务，请进入 '$PROJECT_DIR' 目录并运行 'docker compose down'"
