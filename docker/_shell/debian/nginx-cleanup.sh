#!/bin/bash

# Nginx Cleanup Script for Debian
# This script removes Nginx installation and related configurations

echo "Starting Nginx cleanup..."

# Configuration
NGINX_CONFIG_DIR="/etc/nginx"
NGINX_USER="www-data"
NGINX_GROUP="www-data"

# Stop and disable Nginx service
echo "Checking Nginx service status..."
if systemctl list-unit-files | grep -q nginx; then
    echo "Stopping and disabling Nginx service..."
    systemctl stop nginx
    systemctl disable nginx
fi

# Remove service files
SYSTEMD_FILES=(
    "/etc/systemd/system/nginx.service"
    "/lib/systemd/system/nginx.service"
)

for service_file in "${SYSTEMD_FILES[@]}"; do
    if [ -f "$service_file" ]; then
        echo "Removing service file: $service_file"
        rm -f "$service_file"
    fi
done

# Reload systemd
systemctl daemon-reload

# Remove certbot renewal tasks
echo "Checking and removing certificate renewal tasks..."
CERTBOT_FILES=(
    "/etc/cron.d/certbot"
    "/etc/cron.d/certbot-renewal"
)

for certbot_file in "${CERTBOT_FILES[@]}"; do
    if [ -f "$certbot_file" ]; then
        echo "Removing: $certbot_file"
        rm -f "$certbot_file"
    fi
done

# Backup SSL certificates
if [ -d "/etc/letsencrypt" ]; then
    echo "Backing up SSL certificates..."
    backup_file="/root/letsencrypt-backup-$(date +%Y%m%d).tar.gz"
    tar -czf "$backup_file" -C / etc/letsencrypt
    echo "SSL certificates backed up to: $backup_file"
fi

# Remove Nginx and related packages
echo "Removing Nginx packages..."
apt-get remove --purge -y nginx nginx-common nginx-full
apt-get autoremove -y

# Clean up directories
CLEANUP_DIRS=(
    "/etc/nginx"
    "/var/log/nginx"
    "/var/cache/nginx"
    "/usr/share/nginx"
    "/var/www/html"
)

for dir in "${CLEANUP_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Removing directory: $dir"
        rm -rf "$dir"
    fi
done

echo "Cleanup complete!"
