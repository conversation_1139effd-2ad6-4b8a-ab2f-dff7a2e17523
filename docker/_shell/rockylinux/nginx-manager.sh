#!/bin/bash

# Nginx Manager Script for Docker Services
# This script manages Nginx configurations for Docker services with SSL support

# Comment out set -e temporarily for debugging
# set -e

# Add debug mode
DEBUG=true

# Function to log debug messages
debug_log() {
    if [ "$DEBUG" = true ]; then
        echo -e "${GREEN}DEBUG:${NC} $1"
    fi
}

# Configuration
NGINX_CONFIG_DIR="/usr/local/nginx/conf"
NGINX_SITES_DIR="${NGINX_CONFIG_DIR}/conf.d"
CERTBOT_DIR="/etc/letsencrypt"
DOCKER_NETWORK="nginx-proxy"
CONFIG_FILE="nginx-services.conf"
NGINX_VERSION="1.24.0"
NGINX_INSTALL_DIR="/usr/local/nginx"
NGINX_SRC_DIR="/usr/local/src"
NGINX_USER="nginx"
NGINX_GROUP="nginx"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Load service configurations
if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
else
    echo -e "${RED}Error: Configuration file $CONFIG_FILE not found${NC}"
    echo "Please create the configuration file and run the script again."
    exit 1
fi

# Validate that SERVICES array is defined and not empty
if [ -z "${SERVICES[*]}" ]; then
    echo -e "${RED}Error: No services defined in $CONFIG_FILE${NC}"
    echo "Please add service configurations to the file and run the script again."
    exit 1
fi

# Function to create Docker network if it doesn't exist
create_network() {
    if ! docker network inspect "$DOCKER_NETWORK" >/dev/null 2>&1; then
        echo "Creating Docker network: $DOCKER_NETWORK"
        docker network create "$DOCKER_NETWORK"
    fi
}

# Function to ensure Nginx is installed with common modules
ensure_nginx() {
    # Check if nginx is already installed with correct version
    if [ -f "${NGINX_INSTALL_DIR}/sbin/nginx" ]; then
        current_version=$("${NGINX_INSTALL_DIR}/sbin/nginx" -v 2>&1 | cut -d'/' -f2)
        if [ "$current_version" = "$NGINX_VERSION" ]; then
            echo "Nginx $NGINX_VERSION is already installed"
            return 0
        fi
    fi

    echo "Installing Nginx $NGINX_VERSION from source..."

    # Install dependencies
    dnf install -y gcc gcc-c++ make pcre pcre-devel zlib zlib-devel openssl openssl-devel epel-release

    # Create nginx user and group
    if ! getent group ${NGINX_GROUP} >/dev/null; then
        groupadd -r ${NGINX_GROUP}
    fi
    if ! getent passwd ${NGINX_USER} >/dev/null; then
        useradd -r -g ${NGINX_GROUP} -s /sbin/nologin ${NGINX_USER}
    fi

    # Download and extract nginx
    cd ${NGINX_SRC_DIR} || exit 1
    if [ ! -f nginx-${NGINX_VERSION}.tar.gz ]; then
        wget http://nginx.org/download/nginx-${NGINX_VERSION}.tar.gz
    fi
    rm -rf nginx-${NGINX_VERSION}
    tar -zxf nginx-${NGINX_VERSION}.tar.gz
    cd nginx-${NGINX_VERSION} || exit 1

    # Configure and compile
    ./configure --prefix=${NGINX_INSTALL_DIR} \
        --user=${NGINX_USER} \
        --group=${NGINX_GROUP} \
        --with-http_ssl_module \
        --with-http_v2_module \
        --with-http_realip_module \
        --with-http_stub_status_module \
        --with-http_gzip_static_module \
        --with-pcre \
        --with-stream \
        --with-threads \
        --with-file-aio && \
    make && make install

    # Create necessary directories
    mkdir -p ${NGINX_INSTALL_DIR}/{conf.d,ssl}

    # Create systemd service file
    cat > /etc/systemd/system/nginx.service << EOF
[Unit]
Description=The NGINX HTTP and reverse proxy server
After=network.target remote-fs.target nss-lookup.target

[Service]
Type=forking
PIDFile=${NGINX_INSTALL_DIR}/logs/nginx.pid
ExecStartPre=${NGINX_INSTALL_DIR}/sbin/nginx -t
ExecStart=${NGINX_INSTALL_DIR}/sbin/nginx
ExecReload=${NGINX_INSTALL_DIR}/sbin/nginx -s reload
ExecStop=/bin/kill -s QUIT \$MAINPID
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

    # Set permissions
    chown -R ${NGINX_USER}:${NGINX_GROUP} ${NGINX_INSTALL_DIR}
    chmod -R 755 ${NGINX_INSTALL_DIR}

    # Create basic nginx.conf
    cat > ${NGINX_INSTALL_DIR}/conf/nginx.conf << EOF
user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
pid ${NGINX_INSTALL_DIR}/logs/nginx.pid;

events {
    use epoll;
    worker_connections 65535;
    multi_accept on;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    include       ${NGINX_INSTALL_DIR}/conf/conf.d/*.conf;

    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                      '\$status \$body_bytes_sent "\$http_referer" '
                      '"\$http_user_agent" "\$http_x_forwarded_for" '
                      '\$request_time \$upstream_response_time';

    access_log  ${NGINX_INSTALL_DIR}/logs/access.log  main buffer=16k;
    error_log   ${NGINX_INSTALL_DIR}/logs/error.log  warn;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    keepalive_requests 100;
    client_max_body_size 20m;

    # Upstream keepalive settings
    proxy_http_version 1.1;
    proxy_set_header Connection "";

    gzip  on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    server_tokens off;
}
EOF

    # Reload systemd and start nginx
    systemctl daemon-reload
    systemctl enable nginx
    systemctl start nginx

    echo "Nginx $NGINX_VERSION has been installed successfully"
}

# Function to manage SSL certificate for a specific domain using cert-manager
manage_cert() {
    local domain=$1
    local action=${2:-obtain}  # obtain, renew, or status
    bash "$(dirname "$0")/cert-manager.sh" "$action" "$domain"
}

# Function to setup all services
setup_services() {
    create_network
    ensure_nginx
    bash "$(dirname "$0")/cert-manager.sh" install

    echo -e "\n${GREEN}Setting up services...${NC}"

    # Stop nginx before processing services
    systemctl stop nginx || debug_log "Failed to stop nginx, continuing anyway..."

    # Process each service
    local success_count=0
    local total_services=${#SERVICES[@]}

    for service in "${SERVICES[@]}"; do
        IFS='|' read -r domain container port ssl <<< "$service"
        echo -e "\n${GREEN}Configuring service: $domain${NC}"
        debug_log "Processing service: domain=$domain, container=$container, port=$port, ssl=$ssl"

        if [ "$ssl" = "true" ]; then
            debug_log "Attempting to manage certificate for $domain"
            if ! manage_cert "$domain" obtain; then
                debug_log "Certificate management failed for $domain"
                continue
            fi
        fi

        if [ "$ssl" = "false" ] || [ -f "/etc/letsencrypt/live/${domain}/fullchain.pem" ]; then
            debug_log "Configuring service for $domain"
            if configure_service "$domain" "$container" "$port" "$ssl"; then
                ((success_count++))
                debug_log "Successfully configured $domain"
            else
                debug_log "Failed to configure $domain"
            fi
        else
            debug_log "SSL certificate not found for $domain"
        fi
    done

    # Final nginx reload and status report
    debug_log "Testing nginx configuration"
    if ${NGINX_INSTALL_DIR}/sbin/nginx -t &>/dev/null; then
        debug_log "Nginx configuration test passed"
        systemctl start nginx || debug_log "Failed to start nginx, continuing anyway..."
        reload_nginx
        echo -e "\n${GREEN}✓${NC} Configuration completed successfully"
        echo -e "${GREEN}✓${NC} $success_count of $total_services services configured"

        # Setup certificate auto-renewal
        bash "$(dirname "$0")/cert-manager.sh" setup-renewal
    else
        echo -e "\n${RED}✗${NC} Nginx configuration test failed"
        ${NGINX_INSTALL_DIR}/sbin/nginx -t  # Show the actual error
        return 1
    fi
}

# Function to configure a single service
configure_service() {
    local domain=$1
    local container=$2
    local port=$3
    local ssl=$4
    local config_dir="${NGINX_INSTALL_DIR}/conf/conf.d"
    local config_file="${config_dir}/${domain}.conf"

    # Ensure config directory exists
    mkdir -p "$config_dir"
    mkdir -p /etc/nginx/conf.d

    # Create configuration based on SSL status
    if [ "$ssl" = "true" ]; then
        cat > "$config_file" << EOF
# Upstream configuration for ${domain}
upstream backend_${domain} {
    server 127.0.0.1:${port} max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${domain};

    ssl_certificate /etc/letsencrypt/live/${domain}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${domain}/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/${domain}/chain.pem;

    # SSL configuration
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # modern configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://backend_${domain};
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

server {
    listen 80;
    listen [::]:80;
    server_name ${domain};
    return 301 https://\$server_name\$request_uri;
}
EOF
    else
        # Non-SSL configuration
        cat > "$config_file" << EOF
# Upstream configuration for ${domain}
upstream backend_${domain} {
    server 127.0.0.1:${port} max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    listen [::]:80;
    server_name ${domain};

    location / {
        proxy_pass http://backend_${domain};
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF
    fi

    # Set proper permissions
    chown root:root "$config_file"
    chmod 644 "$config_file"

    # Create symbolic link for site config
    ln -sf "$config_file" "/etc/nginx/conf.d/${domain}.conf"
}

# Function to reload Nginx configuration
reload_nginx() {
    ${NGINX_INSTALL_DIR}/sbin/nginx -t && systemctl reload nginx
}

# Function to add a new service
add_service() {
    local domain=$1
    local container=$2
    local port=$3
    local ssl=${4:-true}

    # Add to configuration file
    echo "\"$domain|$container|$port|$ssl\"" >> "$CONFIG_FILE"
    configure_service "$domain" "$container" "$port" "$ssl"
    reload_nginx
}

# Function to remove a service
remove_service() {
    local domain=$1
    rm -f "${NGINX_INSTALL_DIR}/conf/conf.d/${domain}.conf"
    reload_nginx
}

# Function to list all configured services
list_services() {
    echo -e "${GREEN}Configured services:${NC}"
    for service in "${SERVICES[@]}"; do
        echo "$service"
    done
}



# Parse command line arguments
case "$1" in
    setup)
        setup_services
        setup_cert_renewal
        ;;
    add)
        if [ "$#" -lt 4 ]; then
            echo "Usage: $0 add <domain> <container> <port> [ssl(true/false)]"
            exit 1
        fi
        add_service "$2" "$3" "$4" "${5:-true}"
        ;;
    remove)
        if [ "$#" -lt 2 ]; then
            echo "Usage: $0 remove <domain>"
            exit 1
        fi
        remove_service "$2"
        ;;
    list)
        list_services
        ;;
    certs)
        if [ -n "$2" ]; then
            bash "$(dirname "$0")/cert-manager.sh" status "$2"
        else
            bash "$(dirname "$0")/cert-manager.sh" status
        fi
        ;;
    renew)
        if [ -n "$2" ]; then
            bash "$(dirname "$0")/cert-manager.sh" renew "$2"
        else
            bash "$(dirname "$0")/cert-manager.sh" renew
        fi
        ;;
    *)
        echo "Usage: $0 {setup|add|remove|list|certs|renew} [domain]"
        echo "Examples:"
        echo "  $0 setup                                    # Setup all services and certificate renewal"
        echo "  $0 add example.com container 3000 true     # Add new service"
        echo "  $0 remove example.com                      # Remove service"
        echo "  $0 list                                    # List all services"
        echo "  $0 certs                                   # Check all certificates status"
        echo "  $0 certs example.com                       # Check specific domain certificate"
        echo "  $0 renew                                   # Force renew all certificates"
        echo "  $0 renew example.com                       # Force renew specific domain certificate"
        exit 1
        ;;
esac
