#!/bin/bash

# Nginx and Certificate Cleanup Script
# 这个脚本用于清理Nginx和SSL证书的所有相关配置

# 添加调试模式
DEBUG=true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# 配置目录
NGINX_INSTALL_DIR="/usr/local/nginx"
NGINX_SRC_DIR="/usr/local/src"
NGINX_USER="nginx"
NGINX_GROUP="nginx"
CERTBOT_DIR="/etc/letsencrypt"
CONFIG_FILE="nginx-services.conf"

# 调试日志函数
debug_log() {
    if [ "$DEBUG" = true ]; then
        echo -e "${GREEN}DEBUG:${NC} $1"
    fi
}

# 显示清理进度函数
show_progress() {
    echo -e "\n${GREEN}=== $1 ===${NC}"
}

# 备份函数
backup_data() {
    local source_dir=$1
    local backup_name=$2

    if [ -d "$source_dir" ]; then
        show_progress "正在备份 $backup_name..."
        local backup_file="/root/${backup_name}-backup-$(date +%Y%m%d_%H%M%S).tar.gz"
        if tar -czf "$backup_file" -C / "$source_dir" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} 已备份到: $backup_file"
            return 0
        else
            echo -e "${RED}✗${NC} 备份失败: $source_dir"
            return 1
        fi
    fi
}

# 清理系统服务
cleanup_services() {
    show_progress "清理系统服务"

    # Nginx服务
    if systemctl list-unit-files | grep -q nginx; then
        debug_log "停止并禁用Nginx服务"
        systemctl stop nginx
        systemctl disable nginx
    fi

    # Certbot服务
    if systemctl list-unit-files | grep -q certbot; then
        debug_log "停止并禁用Certbot服务"
        systemctl stop certbot.timer certbot-renew.timer 2>/dev/null
        systemctl disable certbot.timer certbot-renew.timer 2>/dev/null
    fi

    # 清理服务文件
    local service_files=(
        "/etc/systemd/system/nginx.service"
        "/usr/lib/systemd/system/nginx.service"
        "/lib/systemd/system/nginx.service"
        "/etc/systemd/system/certbot.service"
        "/etc/systemd/system/certbot.timer"
        "/etc/systemd/system/certbot-renew.service"
        "/etc/systemd/system/certbot-renew.timer"
    )

    for file in "${service_files[@]}"; do
        if [ -f "$file" ]; then
            debug_log "移除服务文件: $file"
            rm -f "$file"
        fi
    done

    systemctl daemon-reload
}

# 清理证书相关文件
cleanup_certificates() {
    show_progress "清理证书相关文件"

    # 备份证书
    backup_data "$CERTBOT_DIR" "letsencrypt"

    # 清理证书定时任务
    # 并没有这个目录
    local certbot_cron_dir="/etc/cron.d"
    if [ -d "$certbot_cron_dir" ]; then
        debug_log "清理Certbot定时任务文件"
        rm -f "$certbot_cron_dir/certbot" "$certbot_cron_dir/certbot-renewal"
    else
        debug_log "Certbot定时任务目录不存在: $certbot_cron_dir"
        fi
    done

    # 清理证书钩子
    if [ -d "/etc/letsencrypt/renewal-hooks" ]; then
        debug_log "移除证书更新钩子"
        rm -rf "/etc/letsencrypt/renewal-hooks"
    fi

    # 清理证书目录
    if [ -d "$CERTBOT_DIR" ]; then
        debug_log "移除证书目录: $CERTBOT_DIR"
        rm -rf "$CERTBOT_DIR"
    fi
}

# 清理Nginx相关文件
cleanup_nginx() {
    show_progress "清理Nginx相关文件"

    # 清理安装目录
    if [ -d "$NGINX_INSTALL_DIR" ]; then
        debug_log "清理Nginx安装目录: $NGINX_INSTALL_DIR"
        rm -rf "$NGINX_INSTALL_DIR"
    fi

    # 清理源码
    if [ -d "$NGINX_SRC_DIR" ]; then
        debug_log "清理Nginx源码"
        find "$NGINX_SRC_DIR" -name "nginx-*" -type d -exec rm -rf {} +
        find "$NGINX_SRC_DIR" -name "nginx-*.tar.gz" -type f -delete
    fi

    # 清理其他Nginx目录
    local nginx_dirs=(
        "/etc/nginx"
        "/var/log/nginx"
        "/var/cache/nginx"
        "/usr/share/nginx"
        "/var/www/certbot"
    )

    for dir in "${nginx_dirs[@]}"; do
        if [ -d "$dir" ]; then
            debug_log "移除目录: $dir"
            rm -rf "$dir"
        fi
    done

    # 清理配置文件
    if [ -f "$CONFIG_FILE" ]; then
        debug_log "移除配置文件: $CONFIG_FILE"
        rm -f "$CONFIG_FILE"
    fi
}

# 清理用户和组
cleanup_users() {
    show_progress "清理用户和组"

    if getent passwd "$NGINX_USER" >/dev/null; then
        debug_log "移除用户: $NGINX_USER"
        userdel "$NGINX_USER"
    fi

    if getent group "$NGINX_GROUP" >/dev/null; then
        debug_log "移除组: $NGINX_GROUP"
        groupdel "$NGINX_GROUP"
    fi
}

# 清理包管理器安装的组件
cleanup_packages() {
    show_progress "清理系统包"

    if command -v dnf >/dev/null 2>&1; then
        debug_log "移除dnf包..."
        dnf remove -y nginx nginx-* certbot python3-certbot-nginx 2>/dev/null || true
    fi
}

# 主函数
main() {
    echo -e "${GREEN}开始清理Nginx和证书相关组件...${NC}"

    # 执行清理步骤
    cleanup_services
    cleanup_certificates
    cleanup_nginx
    cleanup_users
    cleanup_packages

    echo -e "\n${GREEN}清理完成！${NC}"
}

# 执行主函数
main
