version: '3.7'

services:
  etcd:
    image: quay.io/coreos/etcd:v3.4.1
    ports:
      - '2379:2379' 
      - '2380:2380'
      - '4001:4001'
    volumes:
      - 'etcd_data:/etcd_data'
    environment:
      TZ: Asia/Shanghai
      ETCD_DATA_DIR: /etcd_data
      ETCD_NAME: etcd0
      ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379,http://0.0.0.0:4001
      ETCD_LISTEN_PEER_URLS: http://0.0.0.0:2380
      ETCD_ADVERTISE_CLIENT_URLS: http://galera_etcd:2379,http://galera_etcd:4001
      ETCD_INITIAL_ADVERTISE_PEER_URLS: http://galera_etcd:2380
      ETCD_INITIAL_CLUSTER: etcd0=http://galera_etcd:2380
      ETCD_INITIAL_CLUSTER_STATE: new
      ETCD_INITIAL_CLUSTER_TOKEN: etcd-cluster-1
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]
      restart_policy:
        condition: on-failure
        delay: 8s
        max_attempts: 3
        window: 120s
    networks:
      - galera

  pxc:
    image: percona/percona-xtradb-cluster:5.7
    ports:
      - '3306:3306'
    volumes:
      - 'pxc_data:/var/lib/mysql'
    environment:
      DISCOVERY_SERVICE: galera_etcd:2379
      CLUSTER_NAME: percona_cluster
      MYSQL_ROOT_PASSWORD: 123456
      XTRABACKUP_PASSWORD: 123456
      TZ: Asia/Shanghai
    deploy:
      mode: global
      restart_policy:
        condition: on-failure
        delay: 8s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3  
    networks:
      - galera
  visualizer:
    image: dockersamples/visualizer
    ports:
      - "9001:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]

  portainer:
    image: portainer/portainer
    ports:
      - "9000:9000"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]

networks:
  galera:

volumes:
  etcd_data:
    name: etcd_data
  pxc_data:
    name: pxc_data
