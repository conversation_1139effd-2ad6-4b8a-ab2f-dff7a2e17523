services:
  db:
    image: 'mysql:5.7'
    container_name: mysql5
    hostname: mysql5
    user: "999:999"
    command: '--default-authentication-plugin=mysql_native_password'
    ports:
      - '3307:3306'
    volumes:
      - './data:/var/lib/mysql'
      - './conf:/etc/mysql/conf.d:ro'
      - './init:/docker-entrypoint-initdb.d'
    networks:
      - net_v1
    environment:
      MYSQL_ROOT_PASSWORD: '123456'
      TZ: Asia/Shanghai
    restart: unless-stopped
    logging:
      driver: json-file
      options:
        max-size: 100M
        max-file: '10'
networks:
  net_v1:
    external: true
