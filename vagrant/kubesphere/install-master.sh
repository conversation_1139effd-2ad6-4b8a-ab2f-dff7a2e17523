#!/bin/bash

env=$1

# 安装kubekey
export KKZONE=cn
curl -sfL https://get-kk.kubesphere.io | VERSION=v3.0.13 sh -
chmod +x kk

# 通过kubekey安装k8s、k3s集群和kubesphere
echo "yes" | ./kk create cluster -f /kubesphere/config-$env.yaml

# 避免使用heml时报warnning ==> WARNING: Kubernetes configuration file is group-readable. This is insecure. Location: /root/.kube/config
chmod -R 600 /root/.kube/config

# 安装openelb教程：ttps://itcn.blog/p/1826140548.html

# 安装内网穿透frp
