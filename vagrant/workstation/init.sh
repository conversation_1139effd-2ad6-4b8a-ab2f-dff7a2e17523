#!/bin/bash

# echo -e "\n\n=========== 指定root密码 ================"
# echo "$5" | sudo passwd --stdin root


echo -e "\n\n=========== 安装 docker ================"
sudo yum install -y yum-utils device-mapper-persistent-data lvm2
sudo yum-config-manager --add-repo http://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
sudo yum makecache fast
sudo yum -y install docker-ce docker-ce-cli containerd.io docker-compose-plugin

echo -e "\n\n=========== 自启动 docker ================"
systemctl enable docker
systemctl start docker


echo -e "\n\n=========== 配置国内镜像加速 ================"
mkdir -p /etc/docker
tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://ustc-edu-cn.mirror.aliyuncs.com/",
    "https://52emalvj.mirror.aliyuncs.com",
    "https://docker.mirrors.ustc.edu.cn"
   ]
}
EOF
systemctl daemon-reload
systemctl restart docker

echo -e "\n\n=========== 通过docker安装mysql redis rocketmq kuboard-spray kuboard ================"
git clone https://gitee.com/ibootz/docker-compose.git /root/docker-compose

docker network create net_v1

# cd /root/docker-compose/redis
# docker compose -f docker-compose.yml up -d

# cd /root/docker-compose/mysql
# chmod 644 /root/docker-compose/mysql/conf/mysqld.cnf
# docker compose -f docker-compose.yml up -d

# 用于快速安装k3s（相比kubernetes更轻量级的，但是完全兼容k8s）
# docker compose -f /root/docker-compose/autok3s/docker-compose.yml up -d
# TODO 安装Rancher容器管理平台
# docker compose -f /root/docker-compose/rancher/docker-compose.yml up -d

# TODO 青云开源的k8s管理平台
# Kubesphere

# 安装kuboard，国内一流的k8s管理客户端
# docker compose -f /root/docker-compose/kuboard/docker-compose.yml up -d
# docker compose -f /root/docker-compose/kuboard-spray/docker-compose.yml up -d



echo -e "\n\n=========== 开启docker socket协议, 允许宿主机idea连接 ================"
sed -i 's/-H fd:\/\//-H fd:\/\/ -H tcp:\/\/0.0.0.0:2375 -H unix:\/\/var\/run\/docker.sock/g' /usr/lib/systemd/system/docker.service
systemctl daemon-reload
systemctl restart docker
curl 127.0.0.1:2375/info


echo -e "\n\n=========== 登录私有镜像仓库 ================"
docker login -u zhangq -p 1990912 dockerhub.qingcloud.com
docker login -u <EMAIL> -p XIAOqiang@2023-1 registry.cn-hangzhou.aliyuncs.com

# echo -e "\n\n=========== 安装nfs ================"
# yum install -y rpcbind nfs-utils
# mkdir /mnt/storage
# chmod 777 -R /mnt
# cat >>/etc/exports<<EOF
# /mnt/storage *(rw,sync,no_root_squash,no_subtree_check)
# EOF
# systemctl enable rpcbind
# systemctl enable nfs-server
# systemctl start rpcbind
# systemctl start nfs-server
# exportfs -r
