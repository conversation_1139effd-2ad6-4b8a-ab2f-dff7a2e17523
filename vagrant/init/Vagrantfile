# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.require_version ">= 2.2.0"

# 全局变量
$vagrant_box = "zhangq/centos7"
$network_identifier = "public_network"
$master_name_prefix = "rainbond-master"

Vagrant.configure("2") do |config|

  config.vm.box_check_update = false

  if Vagrant.has_plugin?("vagrant-vbguest")
    config.vbguest.auto_update=false
    config.vbguest.no_install=false
    config.vbguest.no_remote=true
  end

  # 读取环境变量来确定使用哪个配置文件
  env = ENV['VAGRANT_ENV'] || 'home'
  config_file = "env-#{env}.rb"

  # 加载配置文件
  if File.exist?(config_file)
    puts "======>#{config_file}"
    require_relative config_file
  else
    raise "Config file #{config_file} not found"
  end

  config.ssh.insert_key = false
  config.ssh.forward_agent = true

  config.vm.box = $vagrant_box

  ip_prefix = $ip_start.split(".")[0..2].join(".")
  ip_postfix = $ip_start.split(".")[3].to_i

  (1..$master_count).each do |i|
    master_ip = "#{ip_prefix}.#{ip_postfix-1+i}"
    node_name = $master_name_prefix + "#{i}"
    config.vm.define node_name do |master|
      master.vm.network $network_identifier, ip: master_ip, netmask: $netmask, gateway: $gateway
      master.vm.hostname = node_name
      master.vm.provider "virtualbox" do |vb|
        vb.memory = $master_memory
        vb.cpus = $master_cpu
        vb.name = node_name
      end
    end
  end

  if $worker_count > 0
    (1..$worker_count).each do |i|
      worker_ip = "#{ip_prefix}.#{ip_postfix+i}"
      node_name = $worker_name_prefix + "#{i}"
      config.vm.define node_name do |worker|
        worker.vm.network $network_identifier, ip: worker_ip, netmask: $netmask, gateway: $gateway
        worker.vm.hostname = node_name
        worker.vm.provider "virtualbox" do |vb|
          vb.memory = $worker_memory
          vb.cpus = $worker_cpu
          vb.name = node_name
        end
      end
    end
  end

  config.vm.provision "shell", run: "always", privileged: true, inline: <<-SHELL, args: [$dns1, $dns2]
    if ! grep -qxF "nameserver $1" /etc/resolv.conf; then
      echo "nameserver $1" > /etc/resolv.conf
    fi
    if ! grep -qxF "nameserver $2" /etc/resolv.conf; then
      echo "nameserver $2" >> /etc/resolv.conf
    fi
  SHELL

end