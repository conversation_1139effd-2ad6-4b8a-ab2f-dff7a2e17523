# init 文件夹脚本说明

这个 `init` 文件夹包含了一组用于配置和管理 Vagrant 虚拟机的 Ruby 脚本。
它的主要目的是初始化三个（可自定义数量）纯净版的centos7集群，便于后续手动配置。
主要内容如下：

## Vagrantfile

`Vagrantfile` 是 Vagrant 项目的主要配置文件，用于定义虚拟机的配置及行为：

- **Vagrant 版本要求**: 确保 Vagrant 版本 >= 2.2.0。
- **全局变量**: 定义了一些常用的全局变量，如虚拟机镜像、网络标识符、主节点前缀等。
- **插件配置**: 检查并配置 `vagrant-vbguest` 插件。
- **环境配置文件加载**: 根据环境变量 `VAGRANT_ENV` 决定加载哪一个环境配置文件 (`env-home.rb` 或 `env-comp.rb`)。
- **SSH 配置**: 禁止插入默认 SSH 密钥并启用 SSH 代理转发。
- **网络和资源配置**: 为主节点和工作节点设置网络、主机名、内存和 CPU 配置。
- **DNS 配置**: 用 shell 脚本方式设置虚拟机的 DNS。

## env-home.rb

`env-home.rb` 提供了 "home" 环境下的配置变量，包括：

- **IP 地址及网络配置**: 设置起始 IP 地址、子网掩码和网关。
- **DNS 服务器**: 两个 DNS 服务器地址。
- **主节点配置**: 主节点的数量、CPU 和内存分配。
- **工作节点配置**: 工作节点的数量、CPU 和内存分配。

## env-comp.rb

`env-comp.rb` 提供了 "comp" 环境下的配置变量，内容与 `env-home.rb` 类似，但具体的配置参数不同。

这些脚本的主要作用是通过 Vagrant 自动化创建和管理虚拟机，以便更方便地部署和测试各种环境配置。
