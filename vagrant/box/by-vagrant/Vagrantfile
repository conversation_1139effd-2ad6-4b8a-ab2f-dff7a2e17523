# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|

  # 设置主机名
  config.vm.hostname = $name

  # 根据环境加载配置文件
  config_file = "./env/env-base.rb"
  if File.exist?(config_file)
    puts "加载配置文件: #{config_file}"
    require_relative config_file
  else
    raise "配置文件 #{config_file} 未找到"
  end

  # 选择供应商和box
  provider = $vagrant_provider
  box = $vagrant_box

  # 配置 VirtualBox
  if provider == 'virtualbox'
    config.vm.box = box
    # config.vm.box_url = "https://mirrors.ustc.edu.cn/rocky/9/images/x86_64/Rocky-9-Vagrant-Vbox-9.4-20240509.0.x86_64.box"
    config.vm.box_check_update = false

    config.vm.provider "virtualbox" do |vb|
      vb.gui = false
      vb.name = $name
    end

  # 配置 VMware Workstation
  elsif provider == 'vmware_desktop'
    config.vm.box = box
    # config.vm.box_url = "https://mirrors.ustc.edu.cn/rocky/9/images/x86_64/Rocky-9-Vagrant-VMware-9.4-20240509.0.x86_64.box"
    config.vm.box_check_update = false

    config.vm.provider "vmware_desktop" do |vmware|
      vmware.vmx["displayName"] = $name
    end
  else
    raise "不支持的供应商: #{provider}"
  end

  # vbguest 插件配置
  if Vagrant.has_plugin?("vagrant-vbguest")
    config.vbguest.auto_update = false
    config.vbguest.no_remote = true
    config.vbguest.no_install = true
  elsif provider == 'virtualbox'
    # 从国内镜像下载 vagrant-vbguest 插件
    system('vagrant plugin install vagrant-vbguest --plugin-source https://gems.ruby-china.com')
  end

  # 初始化脚本
  config.vm.provision "shell", path: "scripts/provision.sh"
  # 清理脚本, 设置box的ssh配置
  # config.vm.provision "shell", path: "scripts/box.sh"

end
