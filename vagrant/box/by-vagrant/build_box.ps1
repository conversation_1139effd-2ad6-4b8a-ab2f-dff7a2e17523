# 设置变量
$BOX_NAME = "rockylinux-9-vmware-base"
$PROVIDER = "vmware_desktop"
$VERSION = "1.0.0"

# 构建box
vagrant package --output "$BOX_NAME.box"

# 添加box到本地
vagrant box add $BOX_NAME "$BOX_NAME.box" --provider $PROVIDER --force

# 需要提前登录
# vagrant cloud auth login

# 上传box到Vagrant Cloud，推荐直接到Vagrant Cloud官网操作
# vagrant cloud publish $BOX_NAME $VERSION $PROVIDER "$BOX_NAME.box" --release --force

# 复制box文件到指定目录
Copy-Item "$BOX_NAME.box" "E:\BaiduSyncdisk\VagrantBox\$BOX_NAME.box"

Write-Host "Box $BOX_NAME version $VERSION for provider $PROVIDER has been built and uploaded to Vagrant Cloud."
