packer {
  required_version = ">= 1.7.0"
  required_plugins {
    virtualbox = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/virtualbox"
    }
    vmware = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/vmware"
    }
    vagrant = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/vagrant"
    }
  }
}

variable "boot_wait"{
  default = "5s"
}

variable "version"{
  default = "1.0.{{timestamp}}"
}

variable "cpu"{
  default = "2"
}

variable "memory"{
  default = "4096"
}

variable "disk_size"{
  default = "40960"
}

variable "iso_url"{
  default = "file:///E:/BaiduSyncdisk/ISO/Rocky-9.4-x86_64-minimal.iso"
}

variable "iso_checksum"{
  default = "ee3ac97fdffab58652421941599902012179c37535aece76824673105169c4a2"
}

source "vmware-iso" "rockylinux-9-base-vmware"{
  vm_name          = "rockylinux-9-base-vmware-${var.version}"
  guest_os_type    = "centos-64"
  iso_url          = "${var.iso_url}"
  iso_checksum     = "sha256:${var.iso_checksum}"
  ssh_username     = "vagrant"
  ssh_password     = "vagrant"
  ssh_timeout      = "20m"
  shutdown_command = "echo 'vagrant' | sudo -S shutdown -P now"
  cpus             = "${var.cpu}"
  memory           = "${var.memory}"
  disk_size        = "${var.disk_size}"
  vmx_data = {
    "ethernet0.pcislotnumber" = "33"
  }
  http_directory   = "http"
  boot_command     = [
    "<tab><bs><bs><bs><bs><bs>inst.text inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg<enter><wait>"
  ]
  headless         = true
}

source "virtualbox-iso" "rockylinux-9-base-virtualbox"{
  vm_name          = "rockylinux-9-base-virtualbox-${var.version}"
  guest_os_type    = "RedHat_64"
  iso_url          = "${var.iso_url}"
  iso_checksum     = "sha256:${var.iso_checksum}"
  ssh_username     = "vagrant"
  ssh_password     = "vagrant"
  ssh_timeout      = "20m"
  shutdown_command = "echo 'vagrant' | sudo -S shutdown -P now"
  cpus             = "${var.cpu}"
  memory           = "${var.memory}"
  disk_size        = "${var.disk_size}"
  http_directory   = "http"
  boot_command     = [
    "<tab><bs><bs><bs><bs><bs>inst.text inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg<enter><wait>"
  ]
  headless         = true
}

build {
  sources = [
    "source.vmware-iso.rockylinux-9-base-vmware",
    "source.virtualbox-iso.rockylinux-9-base-virtualbox"
  ]

  # 如果下载很慢，这里可以直接本地下载之后复制进虚拟机
  # provisioner "file"{
  #   source      = "plugins/VBoxGuestAdditions.iso"
  #   destination = "/tmp/VBoxGuestAdditions.iso"
  # }

  provisioner "file"{
    source      = "clash/clash-for-linux-install-master.zip"
    destination = "/tmp/clash-for-linux-install-master.zip"
  }

  provisioner "file"{
    source      = "clash/config.yaml"
    destination = "/tmp/config.yaml"
  }

  provisioner "shell" {
    execute_command = "echo 'vagrant'|{{.Vars}} sudo -S -E bash '{{.Path}}'"
    scripts = [
      "scripts/provision.sh",
      "scripts/vagrant.sh",
      "scripts/vmtools.sh",
      "scripts/clean.sh",
      "scripts/zerodisk.sh"
    ]
  }

  post-processor "vagrant"{
    output = "rockylinux-9-base-{{.Provider}}.box"
  }
}
