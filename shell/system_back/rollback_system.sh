#!/bin/bash

# 本脚本使用rsync还原系统

# 1. 检查是否以root用户运行
if [ $UID -ne 0 ]; then
    echo "请以root用户运行本脚本"
    exit 1
fi

# 2. 检查是否存在/backup目录
if [ ! -d /backup ]; then
    echo "备份目录不存在"
    exit 1
fi

# 3. 选择要还原的备份
echo "请选择要还原的备份:"

# 获取备份目录列表, 并去除末尾的斜杠
backup_dirs=($(ls -d /backup/*/ | sort -r | sed 's/\/$//'))

# 显示备份选项
for i in "${!backup_dirs[@]}"; do
    echo "$((i+1))) ${backup_dirs[i]}"
done

# 用户选择
read -p "请输入选项编号: " choice

# 验证用户输入
if [[ ! $choice =~ ^[0-9]+$ ]] || [ $choice -lt 1 ] || [ $choice -gt ${#backup_dirs[@]} ]; then
    echo "无效的选择，请重新运行脚本。"
    exit 1
fi

# 获取选择的备份目录
selected_backup="${backup_dirs[$((choice-1))]}"

# 定义排除列表
EXCLUDE_LIST="--exclude=/mnt/* \
              --exclude=/media/* \
              --exclude=/lost+found \
              --exclude=/backup \
              --exclude=/backup/* \
              --exclude=/root

# 模拟还原并显示将要更改的文件
simulate_restore() {
    echo "执行模拟还原，显示将要更改的文件:"
    rsync -avnc --delete $EXCLUDE_LIST $selected_backup/localhost/ / | grep -v "^sending incremental file list$" | grep -v "^$" | grep -v "^.$"
    echo "模拟还原完成。以上是将要更改的文件列表。"
}

# 执行实际还原
perform_restore() {
    echo "开始还原系统..."
    rsync -av --delete $EXCLUDE_LIST $selected_backup/localhost/ /
    echo "系统还原完成。请检查系统状态，并考虑重启以应用所有更改。"
}

# 主程序逻辑
main() {
    simulate_restore

    read -p "确定要还原此备份吗？(y/n): " confirm
    if [[ $confirm != [Yy] ]]; then
        echo "操作已取消。"
        exit 0
    fi

    perform_restore
}

# 执行主程序
main
