# macOS 开发环境初始化脚本

这个目录包含了用于在macOS系统上设置现代化开发环境的脚本，复刻了WSL Ubuntu环境的功能。

## 脚本列表

### 1. setup_terminal_tools.sh
配置现代化终端环境，包括：
- Homebrew包管理器
- Zsh + Oh My Zsh
- Oh My Posh主题
- Nerd Fonts字体
- 现代CLI工具集（eza, bat, ripgrep, fd, zoxide, fzf, btop, jq）

### 2. setup_asdf_dev_env.sh ⭐ **新增**
配置asdf多语言开发环境，包括：
- 通过Homebrew安装asdf版本管理器
- 安装核心开发语言：
  - Java (Temurin 21)
  - Node.js (LTS版本)
  - Python (最新版本)
  - Go (最新版本)
- 配置开发工具：
  - Maven (Java构建工具)
  - uv (现代化pip替代品)
  - pnpm & yarn (Node.js包管理器)
- macOS特定优化配置

## 使用方法

### 前置要求

1. **安装Homebrew** (如果尚未安装)：
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **安装Xcode Command Line Tools** (如果尚未安装)：
   ```bash
   xcode-select --install
   ```

### 执行脚本

1. **设置终端环境**：
   ```bash
   cd /Users/<USER>/Workspace/tools/scripts/shell/init_mac
   ./setup_terminal_tools.sh
   ```

2. **设置开发环境**：
   ```bash
   ./setup_asdf_dev_env.sh
   ```

### 执行顺序建议

建议按以下顺序执行脚本以获得最佳体验：

1. 首先执行 `setup_terminal_tools.sh` 配置基础终端环境
2. 重启终端或执行 `source ~/.zshrc`
3. 然后执行 `setup_asdf_dev_env.sh` 配置开发环境

## 功能特性

### 与WSL版本的对比

| 功能 | WSL版本 | macOS版本 | 说明 |
|------|---------|-----------|------|
| asdf安装方式 | 预编译二进制 | Homebrew | macOS使用Homebrew更稳定 |
| 包管理器 | apt | Homebrew | 适配macOS生态 |
| 字体安装 | 手动下载 | Homebrew Cask | 更简洁的安装方式 |
| 系统集成 | systemd | launchd | 适配macOS服务管理 |
| 架构支持 | x86_64/arm64 | Intel/Apple Silicon | 自动检测并适配 |

### macOS特定优化

- **Apple Silicon支持**：自动检测M1/M2芯片并使用正确的Homebrew路径
- **Homebrew集成**：完全基于Homebrew生态，确保包管理的一致性
- **系统兼容性**：适配macOS的文件系统和权限模型
- **性能优化**：利用macOS的本地编译优势

## 安装的开发语言和工具

### 核心语言
- **Java**: Temurin 21 (Eclipse基金会发行版)
- **Node.js**: LTS版本 (长期支持版本)
- **Python**: 最新稳定版本
- **Go**: 最新稳定版本

### 开发工具
- **Maven**: Java项目构建工具
- **uv**: 现代化Python包管理器
- **pnpm**: 高效的Node.js包管理器
- **yarn**: 经典Node.js包管理器

### 终端工具
- **eza**: 现代化的ls替代品
- **bat**: 语法高亮的cat替代品
- **ripgrep**: 高性能文本搜索工具
- **fd**: 现代化的find替代品
- **zoxide**: 智能目录跳转工具
- **fzf**: 模糊搜索工具
- **btop**: 现代化系统监控工具
- **jq**: JSON处理工具

## 配置文件

脚本会自动配置以下文件：

- `~/.zshrc`: Zsh配置文件
- `~/.asdfrc`: asdf配置文件
- `~/.tool-versions`: 全局语言版本配置

## 验证安装

执行完脚本后，可以通过以下命令验证安装：

```bash
# 验证asdf
asdf --version
asdf list

# 验证语言环境
java --version
node --version
python --version
go version

# 验证工具
mvn --version
uv --version
pnpm --version
```

## 故障排除

### 常见问题

1. **Homebrew未安装**：
   - 错误信息：`Homebrew未安装`
   - 解决方案：先安装Homebrew

2. **权限问题**：
   - 错误信息：`Permission denied`
   - 解决方案：确保脚本有执行权限 `chmod +x *.sh`

3. **网络问题**：
   - 错误信息：下载失败
   - 解决方案：检查网络连接，必要时配置代理

### 重新安装

如果需要重新安装，可以：

1. 清理asdf：`rm -rf ~/.asdf`
2. 清理配置：备份并重新生成 `~/.zshrc`
3. 重新执行脚本

## 更新日志

- **v1.0**: 初始版本，复刻WSL环境功能
- 支持Apple Silicon和Intel Mac
- 基于Homebrew的包管理
- 完整的多语言开发环境配置
