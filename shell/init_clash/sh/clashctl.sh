# 开启关闭各种常用开发工具的代理

# 检查命令是否存在
function command_exists() {
    which "$1" >/dev/null 2>&1
}

# 检查clash是否已安装
function check_clash() {
    [ -f /usr/local/bin/clash ] || {
        echo "错误: clash未安装!"
        return 1
    }
}

# 检查clash服务状态
function clash_status() {
    systemctl status clash >/dev/null 2>&1
    return $?
}

function clashon() {
    check_clash || return 1

    # 先启动clash服务
    if ! clash_status; then
        systemctl start clash || {
            echo 'clash: 启动失败,执行 "systemctl status clash" 查看日志'
            return 1
        }
        echo 'clash: 启动成功!'
    else
        echo 'clash: 已经在运行!'
    fi

    # 设置系统代理环境变量
    cat <<EOF >/etc/clash/clashenv.sh
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890

echo "系统代理已开启"
EOF

source /etc/clash/clashenv.sh

    # 检查docker并设置代理
    if command_exists docker; then
        # 创建docker配置目录
        mkdir -p /etc/docker
        # 创建或更新 daemon.json
        cat <<'EOF' >/etc/docker/daemon.json
{
    "registry-mirrors": [
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com",
        "https://mirror.baidubce.com"
    ],
    "max-concurrent-downloads": 3,
    "max-concurrent-uploads": 3,
    "debug": true,
    "log-level": "info",
    "proxies": {
        "http-proxy": "http://127.0.0.1:7890",
        "https-proxy": "http://127.0.0.1:7890",
        "no-proxy": "localhost,127.0.0.1,.docker.internal"
    }
}
EOF
        # 重新加载docker配置
        systemctl daemon-reload
        systemctl restart docker
        echo "Docker代理已开启"
    fi

    # 检查npm并设置代理
    if command_exists npm; then
        npm config set proxy "http://127.0.0.1:7890" && \
        npm config set https-proxy "http://127.0.0.1:7890" && \
        echo "npm代理已开启" || echo "npm代理设置失败"
    fi

    # 检查git并设置代理
    if command_exists git; then
        git config --global http.proxy "http://127.0.0.1:7890" && \
        git config --global https.proxy "http://127.0.0.1:7890" && \
        echo "git代理已开启" || echo "git代理设置失败"
    fi

    # 检查yarn并设置代理
    if command_exists yarn; then
        yarn config set proxy "http://127.0.0.1:7890" && \
        yarn config set https-proxy "http://127.0.0.1:7890" && \
        echo "yarn代理已开启" || echo "yarn代理设置失败"
    fi

    return 0
}

function clashoff() {
    # 先清除系统代理环境变量
    cat <<EOF >/etc/clash/clashenv.sh
unset http_proxy
unset https_proxy
unset all_proxy
EOF
    source /etc/clash/clashenv.sh
    echo "系统代理已关闭"

    # 检查docker并清除代理
    if command_exists docker; then
        # 恢复 daemon.json 到基础配置
        cat <<'EOF' >/etc/docker/daemon.json
{
    "registry-mirrors": ["https://docker.mirrors.ustc.edu.cn"]
}
EOF
        # 重新加载docker配置
        systemctl restart docker
        echo "Docker代理已关闭"
    fi

    # 检查npm并清除代理
    if command_exists npm; then
        npm config delete proxy && \
        npm config delete https-proxy && \
        echo "npm代理已关闭" || echo "npm代理清除失败"
    fi

    # 检查git并清除代理
    if command_exists git; then
        git config --global --unset http.proxy && \
        git config --global --unset https.proxy && \
        echo "git代理已关闭" || echo "git代理清除失败"
    fi

    # 检查yarn并清除代理
    if command_exists yarn; then
        yarn config delete proxy && \
        yarn config delete https-proxy && \
        echo "yarn代理已关闭" || echo "yarn代理清除失败"
    fi

    # 最后停止clash服务
    if clash_status; then
        systemctl stop clash && {
            echo 'clash: 成功关闭代理!'
        } || {
            echo 'clash: 关闭失败,执行 "systemctl status clash" 查看日志'
            return 1
        }
    else
        echo 'clash: 已经停止!'
    fi

    return 0
}

# 打开clash控制面板
function clashui() {
    check_clash || return 1
    source /etc/clash/ui.sh
}

# 重启clash服务
function clashreset() {
    check_clash || return 1
    systemctl restart clash && {
        echo 'clash: 重启成功!'
        return 0
    } || {
        echo 'clash: 重启失败,执行 "systemctl status clash" 查看日志'
        return 1
    }
}
