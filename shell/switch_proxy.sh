#!/bin/bash

# 代理服务器信息
proxy_host="127.0.0.1"
proxy_port="7890"
username=""  # 如果代理不需要用户名，则留空
password=""  # 如果代理不需要密码，则留空

# 代理地址
proxy_url="http://$proxy_host:$proxy_port"
if [[ -n "$username" && -n "$password" ]]; then
  proxy_auth="$username:$password@"
  proxy_url="http://$proxy_auth$proxy_host:$proxy_port"
fi

# 检查软件是否存在的函数
check_software() {
  command -v "$1" >/dev/null 2>&1
  if [ $? -ne 0 ]; then
    echo "$1 未安装，代理设置将继续，但可能会影响某些功能。"
  fi
}

# 设置代理的函数
proxyon() {
  # 检查所需软件
  check_software git
  check_software npm
  check_software yarn
  check_software pnpm

  # 设置环境变量代理
  export http_proxy=$proxy_url
  export https_proxy=$proxy_url
  export ftp_proxy=$proxy_url
  export rsync_proxy=$proxy_url
  export no_proxy="localhost,127.0.0.1,localaddress,.localdomain.com"

  # 设置 Git SOCKS 代理
  if command -v git >/dev/null 2>&1; then
    git config --global http.proxy "http://$proxy_host:$proxy_port"
    git config --global https.proxy "http://$proxy_host:$proxy_port"
  fi

  # 设置 NPM 代理
  if command -v npm >/dev/null 2>&1; then
    npm config set proxy $proxy_url
    npm config set https-proxy $proxy_url
  fi

  # 如果使用 NVM，请将 NVM_DIR 改为实际使用的路径
  if [ -d "$NVM_DIR" ]; then
    npm_config_prefix="$NVM_DIR/versions/node/$(nvm current)"
  fi

  # 设置 Yarn 代理
  if command -v yarn >/dev/null 2>&1; then
    yarn config set proxy $proxy_url
    yarn config set https-proxy $proxy_url
  fi

  # 设置 PNPM 代理
  if command -v pnpm >/dev/null 2>&1; then
    pnpm config set proxy $proxy_url
    pnpm config set https-proxy $proxy_url
  fi

  # 设置 Docker 守护进程代理
  sudo mkdir -p /etc/systemd/system/docker.service.d
  echo "[Service]
Environment=\"HTTP_PROXY=$proxy_url\"
Environment=\"HTTPS_PROXY=$proxy_url\"
Environment=\"NO_PROXY=localhost,127.0.0.1\"" | sudo tee /etc/systemd/system/docker.service.d/http-proxy.conf > /dev/null

  # 设置 Docker client 配置
  mkdir -p ~/.docker
  echo "{
  \"proxies\": {
    \"default\": {
      \"httpProxy\": \"$proxy_url\",
      \"httpsProxy\": \"$proxy_url\",
      \"noProxy\": \"localhost,127.0.0.1\"
    }
  }
}" | tee ~/.docker/config.json > /dev/null

  # 设置 Docker daemon 配置
  sudo mkdir -p /etc/docker
  echo "{
  \"registry-mirrors\": [
    \"https://docker.nju.edu.cn\",
    \"https://mirror.ccs.tencentyun.com\",
    \"https://hub-mirror.c.163.com\",
    \"https://mirror.baidubce.com\"
  ]
}" | sudo tee /etc/docker/daemon.json > /dev/null

  # 重启 Docker 服务
  sudo systemctl daemon-reload
  sudo systemctl restart docker

  # 设置 Python 代理
  export PIP_PROXY=$proxy_url

  echo "代理已开启！"
}

# 关闭代理设置的函数
proxyoff() {
  # 检查所需软件
  check_software git
  check_software npm
  check_software yarn
  check_software pnpm

  # 取消设置的环境变量代理
  unset http_proxy
  unset https_proxy
  unset ftp_proxy
  unset rsync_proxy
  unset no_proxy

  # 取消设置 Git 代理
  if command -v git >/dev/null 2>&1; then
    git config --global --unset http.proxy
    git config --global --unset https.proxy
  fi

  # 取消设置 NPM 代理
  if command -v npm >/dev/null 2>&1; then
    npm config delete proxy
    npm config delete https-proxy
  fi

  # 取消设置 Yarn 代理
  if command -v yarn >/dev/null 2>&1; then
    yarn config delete proxy
    yarn config delete https-proxy
  fi

  # 取消设置 PNPM 代理
  if command -v pnpm >/dev/null 2>&1; then
    pnpm config delete proxy
    pnpm config delete https-proxy
  fi

  # 移除 Docker 代理配置
  sudo rm -f /etc/systemd/system/docker.service.d/http-proxy.conf
  rm -f ~/.docker/config.json

  # 设置 Docker daemon 配置（仅保留镜像源）
  sudo mkdir -p /etc/docker
  echo '{
  "registry-mirrors": [
    "https://docker.nju.edu.cn",
    "https://mirror.ccs.tencentyun.com",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}' | sudo tee /etc/docker/daemon.json > /dev/null

  # 重启 Docker 服务
  sudo systemctl daemon-reload
  sudo systemctl restart docker

  # 取消设置 Python 代理
  unset PIP_PROXY

  echo "代理已关闭！"
}

# 在控制台显示提示文本
echo "proxyon: 开启代理      proxyoff: 关闭代理"
