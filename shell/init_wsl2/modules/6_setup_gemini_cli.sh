#!/usr/bin/env bash

# ==============================================================================
# 6.安装 Gemini CLI 工具
# ==============================================================================

setup_gemini_cli() {
    if command -v gemini &> /dev/null; then
        info "Gemini CLI 工具已安装，跳过安装。"
        success "Gemini CLI 工具已准备就绪。"
        return 0
    fi

    info "正在安装 Gemini CLI 工具..."

    # 检查是否已安装 Node.js 18+
    if ! command -v node &> /dev/null; then
        error "未检测到 Node.js，请先安装 Node.js 18 或更高版本"
        return 1
    fi

    # 检查 Node.js 版本
    local node_version
    node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        error "需要 Node.js 18 或更高版本，当前版本: $(node -v)"
        return 1
    fi

    # 检查是否已安装 npm
    if ! command -v npm &> /dev/null; then
        error "未检测到 npm，请先安装 npm"
        return 1
    fi

    # 使用 npm 全局安装 @google/gemini-cli
    info "正在安装 @google/gemini-cli..."
    if npm install -g @google/gemini-cli; then
        success "Gemini CLI 工具安装成功"

        # 提示用户输入 API Key 并配置
        info "请提供您的 Gemini API 密钥以完成配置。"
        echo "您可以从这里获取: https://aistudio.google.com/app/apikey"
        read -p "请输入您的 GEMINI_API_KEY: " gemini_api_key

        if [ -n "$gemini_api_key" ]; then
            local shell_config_file
            if [ -f "$HOME/.zshrc" ]; then
                shell_config_file="$HOME/.zshrc"
            else
                shell_config_file="$HOME/.bashrc"
            fi

            # 将 API Key 添加到 shell 配置文件
            echo "" >> "$shell_config_file"
            echo "# Gemini API Key" >> "$shell_config_file"
            echo "export GEMINI_API_KEY=\"$gemini_api_key\"" >> "$shell_config_file"

            success "Gemini API 密钥已成功添加到 $shell_config_file"
            info "请运行 'source $shell_config_file' 或重新启动终端以使更改生效。"
            info "然后，您可以运行 'gemini' 来开始使用。"
        else
            warning "未提供 API 密钥。请手动配置 GEMINI_API_KEY 环境变量。"
            info "您可以将 'export GEMINI_API_KEY=\"YOUR_API_KEY\"' 添加到您的 shell 配置文件中。"
        fi
    else
        error "Gemini CLI 工具安装失败"
        return 1
    fi
}
