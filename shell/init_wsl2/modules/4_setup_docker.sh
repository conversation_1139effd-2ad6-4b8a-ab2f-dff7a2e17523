#!/usr/bin/env bash

# ==============================================================================
# 4. 容器化环境与付费镜像加速
# ==============================================================================
setup_docker() {
    info "--- 开始配置Docker容器化环境 ---"

    # 5.1 安装Docker Engine CE
    if ! command_exists docker; then
        info "正在安装Docker Engine CE..."
        sudo_with_msg "安装Docker依赖包..." apt-get install -y apt-transport-https ca-certificates curl gnupg-agent software-properties-common
        # 使用阿里云的Docker GPG密钥源
        sudo_with_msg "创建Docker密钥目录..." install -m 0755 -d /etc/apt/keyrings
        curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | sudo_with_msg "导入Docker GPG密钥..." gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        sudo_with_msg "设置Docker密钥权限..." chmod a+r /etc/apt/keyrings/docker.gpg

        echo \
          "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://mirrors.aliyun.com/docker-ce/linux/ubuntu \
          $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
          sudo_with_msg "添加Docker软件源..." tee /etc/apt/sources.list.d/docker.list > /dev/null

        sudo_with_msg "更新软件包列表..." apt-get update
        sudo_with_msg "安装Docker引擎和相关工具..." apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
        success "Docker Engine CE安装完成。"
    else
        info "Docker已安装，跳过。"
    fi

    # 5.2 配置Docker开机自启和免sudo
    info "正在配置Docker服务和用户权限..."
    if systemctl list-unit-files | grep -q 'docker.service'; then
        sudo_with_msg "设置Docker服务开机自启..." systemctl enable docker
        sudo_with_msg "启动Docker服务..." systemctl start docker
    else
        warn "Docker systemd服务未找到，可能需要重启WSL后手动启动。"
    fi

    if ! getent group docker | grep -q "\b$(whoami)\b"; then
        sudo_with_msg "将当前用户添加到docker组..." usermod -aG docker "$(whoami)"
        warn "当前用户已添加到docker组。"
        warn "您需要重新登录WSL或运行以下命令使更改生效："
        echo -e "\n    newgrp docker\n    exec $SHELL\n"
        warn "或者完全退出WSL终端并重新打开。"
    else
        info "当前用户已在docker组中。"
        warn "如果仍然遇到权限问题，请尝试重新登录或运行：newgrp docker"
    fi

    # 5.3 基础Docker配置完成
    success "Docker基础配置已完成。"
    info "--- Docker容器化环境配置完成 ---"
}
