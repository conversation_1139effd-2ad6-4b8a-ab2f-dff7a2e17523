#!/usr/bin/env bash

# ==============================================================================
# 5.多语言开发环境 (基于 asdf 预编译二进制文件)
# ==============================================================================

setup_asdf_dev_env() {
    info "--- 开始配置asdf及多语言开发环境 ---"

    # 定义 asdf 数据目录和安装目录
    ASDF_DATA_DIR="${ASDF_DATA_DIR:-$HOME/.asdf}"
    ASDF_BIN_DIR="$HOME/.local/bin"
    ASDF_VERSION="0.18.0"  # 指定最新稳定版本

    # 检查依赖
    info "检查 asdf 所需的依赖..."
    for dep in git curl bash; do
        if ! command -v $dep &> /dev/null; then
            error "缺少必要依赖: $dep，请先安装后再运行此脚本"
            return 1
        fi
    done
    success "所有基础依赖已满足"

    # 检查是否已安装 asdf
    if command -v asdf &> /dev/null; then
        current_version=$(asdf --version 2>/dev/null | awk '{print $1}' | cut -d 'v' -f2)
        if [ -n "$current_version" ]; then
            info "检测到已安装 asdf 版本: $current_version"

            # 如果版本相同，则跳过安装
            if [ "$current_version" = "$ASDF_VERSION" ]; then
                info "当前 asdf 版本已是最新 ($ASDF_VERSION)，无需更新。"
                install_needed=false
            else
                info "发现新版本 asdf v$ASDF_VERSION，将进行更新..."
                install_needed=true
            fi
        else
            warn "无法确定当前 asdf 版本，将重新安装。"
            install_needed=true
        fi
    else
        info "未检测到 asdf 安装，将执行全新安装..."
        install_needed=true
    fi

    # 如果需要安装或更新
    if [ "${install_needed:-true}" = true ]; then
        # 确保安装目录存在
        mkdir -p "$ASDF_BIN_DIR"
        mkdir -p "$ASDF_DATA_DIR"

        # 确定系统架构和操作系统
        arch=$(uname -m)
        os=$(uname -s | tr '[:upper:]' '[:lower:]')

        # 映射架构名称
        case "$arch" in
            x86_64)
                arch="amd64"
                ;;
            aarch64|arm64)
                arch="arm64"
                ;;
            *)
                error "不支持的系统架构: $arch"
                return 1
                ;;
        esac

        # 构建下载 URL
        download_url="https://github.com/asdf-vm/asdf/releases/download/v${ASDF_VERSION}/asdf-v${ASDF_VERSION}-${os}-${arch}.tar.gz"

        info "从 $download_url 下载 asdf v${ASDF_VERSION} 预编译二进制文件..."

        # 创建临时目录
        temp_dir=$(mktemp -d)

        # 下载并解压
        if curl -L "$download_url" -o "${temp_dir}/asdf.tar.gz"; then
            tar -xzf "${temp_dir}/asdf.tar.gz" -C "$temp_dir"

            # 复制二进制文件到安装目录
            cp "${temp_dir}/asdf" "$ASDF_BIN_DIR/"
            chmod +x "$ASDF_BIN_DIR/asdf"

            # 清理临时文件
            rm -rf "$temp_dir"

            success "asdf v${ASDF_VERSION} 已成功安装到 $ASDF_BIN_DIR/asdf"
        else
            error "下载 asdf 失败，请检查网络连接或手动安装。"
            rm -rf "$temp_dir"
            return 1
        fi
    fi

    # 确保 asdf 在 PATH 中
    if ! echo "$PATH" | grep -q "$ASDF_BIN_DIR"; then
        export PATH="$ASDF_BIN_DIR:$PATH"
    fi

    # 验证安装
    if ! command -v asdf &> /dev/null; then
        error "asdf 安装后无法在 PATH 中找到，请检查安装路径。"
        return 1
    fi

    # --- 配置 shell 环境 ---
    info "检查并确保 shell 环境中的 asdf 配置..."

    # 配置 .zshrc
    if [ -f "$HOME/.zshrc" ]; then
        info "配置 .zshrc 中的 asdf 设置..."

        # 添加 PATH 配置
        if ! grep -q "$ASDF_BIN_DIR" "$HOME/.zshrc"; then
            echo -e "\n# ASDF Version Manager - PATH 配置" >> "$HOME/.zshrc"
            echo "export PATH=\"$ASDF_BIN_DIR:\$PATH\"" >> "$HOME/.zshrc"
            success "asdf PATH 配置已添加到 .zshrc"
        else
            info "asdf PATH 配置已存在于 .zshrc，跳过。"
        fi

        # 添加 ASDF_DATA_DIR 配置
        if ! grep -q "ASDF_DATA_DIR" "$HOME/.zshrc"; then
            echo -e "\n# ASDF 数据目录配置" >> "$HOME/.zshrc"
            echo "export ASDF_DATA_DIR=\"$ASDF_DATA_DIR\"" >> "$HOME/.zshrc"
            success "ASDF_DATA_DIR 配置已添加到 .zshrc"
        else
            info "ASDF_DATA_DIR 配置已存在于 .zshrc，跳过。"
        fi

        # 添加 shims 到 PATH
        if ! grep -q "ASDF_DATA_DIR.*shims" "$HOME/.zshrc"; then
            echo -e "\n# ASDF shims 配置" >> "$HOME/.zshrc"
            echo "export PATH=\"\${ASDF_DATA_DIR}/shims:\$PATH\"" >> "$HOME/.zshrc"
            success "asdf shims PATH 配置已添加到 .zshrc"
        else
            info "asdf shims PATH 配置已存在于 .zshrc，跳过。"
        fi

        # 设置 completions
        if ! grep -q "fpath=.*asdf.*completions" "$HOME/.zshrc"; then
            echo -e "\n# ASDF completions 配置" >> "$HOME/.zshrc"
            echo "mkdir -p \${ASDF_DATA_DIR}/completions" >> "$HOME/.zshrc"
            echo "asdf completion zsh > \${ASDF_DATA_DIR}/completions/_asdf 2>/dev/null || true" >> "$HOME/.zshrc"
            echo "fpath=(\${ASDF_DATA_DIR}/completions \$fpath)" >> "$HOME/.zshrc"
            echo "autoload -Uz compinit && compinit" >> "$HOME/.zshrc"
            success "asdf completions 配置已添加到 .zshrc"
        else
            info "asdf completions 配置已存在于 .zshrc，跳过。"
        fi
    fi

    # 创建 completions 目录
    mkdir -p "${ASDF_DATA_DIR}/completions"

    # 生成 completions 文件
    if command -v asdf &> /dev/null; then
        info "生成 asdf zsh completions 文件..."
        asdf completion zsh > "${ASDF_DATA_DIR}/completions/_asdf" 2>/dev/null || true
        success "asdf completions 文件已生成"
    fi

    # 导出环境变量以便在当前会话中使用
    export ASDF_DATA_DIR="$ASDF_DATA_DIR"
    export PATH="${ASDF_DATA_DIR}/shims:$PATH"

    # 2. 辅助函数定义
    is_language_installed() {
        local lang=$1
        local version=$2

        info "检查 $lang $version 是否已安装..."

        # 首先确保 asdf 命令可用
        if ! command_exists asdf; then
            warn "asdf命令不可用，无法检查语言安装状态"
            return 1
        fi

        # 检查插件是否已安装
        if ! asdf plugin list 2>/dev/null | grep -q "^$lang$"; then
            info "$lang 插件未安装"
            return 1
        fi

        # 获取已安装的版本列表
        local installed_versions
        installed_versions=$(asdf list "$lang" 2>/dev/null)

        # 如果没有安装任何版本
        if [ -z "$installed_versions" ]; then
            info "$lang 没有安装任何版本"
            return 1
        fi

        # 特殊处理Java版本
        if [[ "$lang" == "java" ]]; then
            # 如果要求temurin版本
            if [[ "$version" == *"temurin"* ]]; then
                # 提取请求的具体版本号，例如从"latest:temurin-21"提取"21"
                local requested_version=""
                if [[ "$version" =~ temurin-([0-9]+) ]]; then
                    requested_version="${BASH_REMATCH[1]}"
                    info "请求的Java版本: temurin-$requested_version"
                fi

                # 检查是否有任何temurin版本已安装
                if echo "$installed_versions" | grep -q "temurin"; then
                    local temurin_version
                    temurin_version=$(echo "$installed_versions" | grep "temurin" | sort -V | tail -1 | xargs)
                    info "检测到 $lang 已安装temurin版本: $temurin_version"

                    # 提取已安装版本的主版本号
                    local clean_temurin_version
                    clean_temurin_version=$(echo "$temurin_version" | sed 's/^[ *]*//' | xargs) # 移除星号和多余空格

                    local installed_major_version=""
                    if [[ "$clean_temurin_version" =~ temurin-(jre-)?([0-9]+) ]]; then
                        installed_major_version="${BASH_REMATCH[2]}"
                    fi

                    if [ "$installed_major_version" == "$requested_version" ]; then
                        success "已安装的temurin版本($temurin_version)与请求的版本(temurin-$requested_version)匹配"
                        return 0
                    else
                        info "已安装的temurin版本($temurin_version)与请求的版本(temurin-$requested_version)不匹配"
                        return 1
                    fi
                fi
            fi
        fi

        # 处理latest或lts版本
        if [[ "$version" == "latest"* || "$version" == "lts"* ]]; then
            # 获取最新安装的版本
            local latest_installed
            latest_installed=$(echo "$installed_versions" | sed -E 's/^[ *]*//; s/ *\(.*\)//' | sort -V | tail -1 | xargs)

            if [ -n "$latest_installed" ]; then
                info "检测到 $lang 已安装最新版本: $latest_installed"
                # 设置为全局版本
                (cd "$HOME" && asdf set "$lang" "$latest_installed")
                return 0
            fi
        fi

        # 对于其他语言，检查请求的版本是否在列表中
        # 我们必须清理 `asdf list` 的输出，移除 '*' 标记和注释（如 '(set by ...)'）
        if echo "$installed_versions" | sed -E 's/^[ *]*//; s/ *\(.*\)//' | grep -q -x "$version"; then
            success "$lang $version 已安装"

            # 如果尚未设置，则设置为全局版本。`asdf set <lang> <alias>` 是有效的。
            local current_global_name
            current_global_name=$(asdf current "$lang" 2>/dev/null | awk '{print $1}')

            if [ "$current_global_name" != "$version" ]; then
                # 当前全局版本可能是已解析的版本号（例如 22.2.0）而不是别名（lts）。
                # 使用别名重新设置是幂等的，并确保别名处于活动状态。
                (cd "$HOME" && asdf set "$lang" "$version")
            fi
            return 0
        else
            info "$lang $version 未安装"
            return 1
        fi
    }

    install_language() {
        local lang=$1
        local version=$2
        info "正在处理语言: $lang $version"

        # 首先检查是否已安装。is_language_installed会处理好成功信息的打印和全局版本的设置。
        if is_language_installed "$lang" "$version"; then
            return 0
        fi

        # 确保插件已安装
        if ! asdf plugin list | grep -q "^$lang$"; then
            info "正在安装 asdf-$lang 插件..."
            asdf plugin add "$lang" || { error "安装 asdf-$lang 插件失败"; return 1; }
        else
            info "正在更新 asdf-$lang 插件..."
            asdf plugin update "$lang" 2>/dev/null || warn "更新 $lang 插件失败，将使用本地缓存版本"
        fi

        # 为 nodejs 安装设置国内镜像
        if [ "$lang" = "nodejs" ]; then
            info "检测到 Node.js 安装，临时设置淘宝镜像以加速下载..."
            export NODE_BUILD_MIRROR_URL="https://npmmirror.com/mirrors/node"
        fi

        # 安装指定版本
        info "正在安装 $lang $version..."
        if ! asdf install "$lang" "$version"; then
            error "$lang $version 安装失败。"
            # 清理环境变量
            if [ "$lang" = "nodejs" ]; then
                unset NODE_BUILD_MIRROR_URL
            fi
            return 1
        fi

        # 清理为 nodejs 设置的镜像环境变量
        if [ "$lang" = "nodejs" ]; then
            info "Node.js 安装完成，正在取消镜像设置..."
            unset NODE_BUILD_MIRROR_URL
        fi

        # 检查安装后是否有可用版本
        local available_versions
        available_versions=$(asdf list "$lang" 2>/dev/null)

        if [ -n "$available_versions" ]; then
            # 获取并设置实际安装的版本为全局版本
            local installed_version

            # 对于Java temurin，精确查找刚安装的版本
            if [[ "$lang" == "java" && "$version" == *"temurin"* ]]; then
                local requested_major_version_pattern
                if [[ "$version" =~ temurin-([0-9]+) ]]; then
                    requested_major_version_pattern="temurin-${BASH_REMATCH[1]}"
                    # 从所有可用版本中，筛选出符合主版本要求的最新版
                    installed_version=$(echo "$available_versions" | grep "$requested_major_version_pattern" | sort -V | tail -1 | xargs)
                fi
            fi

            # 如果上述逻辑未找到版本（例如非Java或非temurin请求），则使用通用逻辑
            if [ -z "$installed_version" ]; then
                # 直接使用传入的版本别名（如 lts, latest），asdf set 会自动解析
                installed_version=$version
            fi

            # 设置为全局版本
            (cd "$HOME" && asdf set "$lang" "$installed_version")

            # 获取最终设置的具体版本号用于日志输出
            local final_version
            final_version=$(asdf current "$lang" | awk '{print $2}')
            success "$lang $final_version 安装并设为全局版本。"
            return 0
        else
            error "$lang $version 安装失败，没有找到可用版本"
            return 1
        fi
    }

    # 3. 安装核心语言
    info "--- 开始安装核心开发语言 ---"

    # 创建 .tool-versions 文件（如果不存在）
    if [ ! -f "$HOME/.tool-versions" ]; then
        info "创建全局 .tool-versions 文件..."
        touch "$HOME/.tool-versions"
    fi

    # 安装核心语言
    install_language java latest:temurin-21
    install_language nodejs lts
    install_language python latest
    install_language golang latest

    # 安装和配置其他工具
    info "--- 安装和配置其他开发工具 ---"

    # Java - Maven
    info "安装 Maven..."
    asdf plugin add maven 2>/dev/null || true
    asdf install maven latest &>/dev/null || true
    (cd "$HOME" && asdf set maven latest)
    success "Maven 安装完成。"

    # Python - uv
    info "安装 uv (pip 替代品)..."
    if ! command -v uv &> /dev/null; then
        curl -LsSf https://astral.sh/uv/install.sh | sh
    fi
    if command -v uv &> /dev/null; then
        if ! grep -q "alias pip=" "$HOME/.zshrc"; then
            info "配置 uv 作为 pip 的别名..."
            echo -e '\n# 使用 uv 作为 pip 的替代品\nalias pip="uv pip"\nalias pip3="uv pip"' >> "$HOME/.zshrc"
        fi
        success "uv 已安装并配置为 pip 的别名。"
    fi

    # Node.js - pnpm & yarn
    info "安装 pnpm 和 yarn..."
    if command -v corepack &> /dev/null; then
        corepack enable
        info "使用 corepack 安装 pnpm..."
        corepack prepare pnpm@latest --activate
        info "使用 corepack 安装 yarn..."
        corepack prepare yarn@latest --activate
        info "更新 asdf shims for nodejs..."
        asdf reshim nodejs
        success "pnpm 和 yarn 安装完成。"
    else
        warn "corepack 命令不存在，跳过 pnpm 和 yarn 的安装。请确保 Node.js 版本支持 corepack。"
    fi

    # Go - 开启 go mod
    info "配置 Go 模块..."
    go env -w GO111MODULE=on
    success "Go 模块配置完成。"

    # 配置 .asdfrc 文件（如果不存在）
    if [ ! -f "$HOME/.asdfrc" ]; then
        info "创建 .asdfrc 配置文件..."
        echo "# 启用传统版本文件支持（如 .nvmrc, .node-version 等）" > "$HOME/.asdfrc"
        echo "legacy_version_file = yes" >> "$HOME/.asdfrc"
        success ".asdfrc 配置文件已创建"
    fi

    info "--- asdf及多语言开发环境配置完成 ---"
}
