#!/usr/bin/env bash

# ==============================================================================
# 共享工具函数库
# ==============================================================================

# --- 脚本安全设置 ---
set -eo pipefail

# --- 全局变量与辅助函数 ---
# 使用颜色输出，增强可读性
# 使用$'...'语法确保ANSI转义序列被正确解析
C_RED=$'\033[0;31m'
C_GREEN=$'\033[0;32m'
C_YELLOW=$'\033[0;33m'
C_BLUE=$'\033[0;34m'
C_NC=$'\033[0m' # No Color

# 检测是否支持彩色输出
color_supported() {
    test -t 1 && tput colors >/dev/null 2>&1 && test $(tput colors) -ge 8
}

# 如果终端不支持彩色输出，则禁用颜色
if ! color_supported; then
    C_RED=''
    C_GREEN=''
    C_YELLOW=''
    C_BLUE=''
    C_NC=''
fi

# 日志函数
info() {
    echo -e "${C_BLUE}[INFO]${C_NC} $1"
}

success() {
    echo -e "${C_GREEN}[SUCCESS]${C_NC} $1"
}

warn() {
    echo -e "${C_YELLOW}[WARNING]${C_NC} $1"
}

error() {
    echo -e "${C_RED}[ERROR]${C_NC} $1" >&2
    exit 1
}

# --- sudo权限管理 ---
# 用于减少sudo密码输入次数的函数
sudo_with_msg() {
    local msg="$1"
    shift
    
    # 更新sudo缓存
    sudo -v
    
    info "$msg"
    sudo "$@"
}

# 在脚本开始时维持sudo权限
maintain_sudo() {
    info "正在请求sudo权限，请输入您的密码（如果需要）..."
    sudo -v
    
    # 保持sudo权限有效，每60秒更新一次
    while true; do
        sudo -n true
        sleep 60
        kill -0 "$$$" || exit
    done 2>/dev/null &
    
    SUDO_PID=$!
    success "sudo权限已获取并将在脚本运行期间保持有效。"
}

# 命令存在性检查函数
command_exists() {
    command -v "$1" &> /dev/null
}
