#!/usr/bin/env bash

# ==============================================================================
# 7.最终提示信息
# ==============================================================================

final_instructions() {
    # 使用 echo 命令直接输出，避免依赖外部文件
    cat << 'EOF'

${C_GREEN}==============================================================================${C_NC}
${C_GREEN}            🎉 恭喜！您的开发环境已配置完成！ 🎉            ${C_GREEN}
${C_GREEN}==============================================================================${C_NC}

${C_YELLOW}1. 重启WSL终端:${C_NC}
   运行以下命令使所有配置生效：
   \`wsl --shutdown\`
   然后重新打开WSL终端。

${C_YELLOW}2. 设置Nerd Font字体:${C_NC}
   脚本已在WSL中为您安装了 ${C_GREEN}RobotoMono Nerd Font${C_NC}。
   为了正常显示所有图标和符号，请进行以下操作：
   - ${C_GREEN}打开您的Windows终端 (如: Windows Terminal, Fluent Terminal)。${C_NC}
   - ${C_GREEN}进入设置，将字体更改为 "RobotoMono Nerd Font"。${C_NC}
   - 如果您想尝试其他字体，可以访问: https://www.nerdfonts.com/font-downloads

${C_YELLOW}3. 个人信息配置:${C_NC}
   - Git: 如果脚本未成功获取您的信息，请手动编辑 \`~/.gitconfig\` 设置姓名和邮箱。
   - SSH: 使用以下标准命令生成新的SSH密钥 (推荐使用ed25519算法):
     \`\`\`bash
     ssh-keygen -t ed25519 -C "<EMAIL>"
     \`\`\`
     (请将 <EMAIL> 替换为您自己的邮箱)
   - 将公钥 \`~/.ssh/id_ed25519.pub\` 的内容添加到您的代码托管平台 (如GitHub, Gitee)。

${C_YELLOW}4. Docker 配置 (可选):${C_NC}
   # 配置镜像加速
   curl -s https://static.1ms.run/1ms-helper/scripts/install.sh | sudo bash /dev/stdin config:mirror

   # 付费用户登录 (可选)
   # curl -s https://static.1ms.run/1ms-helper/scripts/install.sh | sudo bash /dev/stdin config:account

   # 重启Docker服务
   sudo systemctl restart docker

${C_GREEN}祝您开发愉快！${C_NC}
EOF
}
