#!/usr/bin/env bash

# ==============================================================================
# 3. 现代化终端与新锐CLI工具
# ==============================================================================

setup_terminal_tools() {
    # --- Helper function to create a clean .zshrc configuration ---
    # By defining it inside, we ensure it's a local helper
    # and that setup_terminal_tools is the only top-level function.
    _create_clean_zshrc() {
        info "--- 开始创建全新的 .zshrc 配置 ---"
        local zshrc_file="$HOME/.zshrc"

        # 备份现有的.zshrc文件（如果存在且不是我们创建的）
        if [ -f "$zshrc_file" ] && ! grep -q "# Cascade Clean ZSH Config" "$zshrc_file"; then
            local backup_file="$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
            info "备份现有的.zshrc文件到 $backup_file"
            cp "$zshrc_file" "$backup_file"
        fi

        # 定义期望的插件列表
        local plugins_list=(
            git
            zsh-syntax-highlighting
            zsh-autosuggestions
            zsh-completions
            sudo
            extract
            cp
            docker
            docker-compose
            kubectl
            npm
            yarn
            pip
            python
            golang
            rust
        )

        # 创建完全干净的.zshrc配置
        info "正在生成全新的.zshrc配置..."
        cat > "$zshrc_file" << 'EOF'
# ==============================================================================
# Cascade Clean ZSH Config
# ==============================================================================

# --- 基础环境配置 ---
# 设置Oh My Zsh安装路径
export ZSH="$HOME/.oh-my-zsh"

# 确保用户本地bin目录在PATH中
export PATH="$HOME/.local/bin:$PATH"

# --- Oh My Zsh配置 ---
# 禁用默认主题，使用Oh My Posh
ZSH_THEME=""

# 启用的插件列表
plugins=(
  git
  zsh-syntax-highlighting
  zsh-autosuggestions
  zsh-completions
  sudo
  extract
  cp
  docker
  docker-compose
  kubectl
  npm
  yarn
  pip
  python
  golang
  rust
)

# 加载Oh My Zsh
source $ZSH/oh-my-zsh.sh

# --- 自动补全配置 ---
autoload -U compinit && compinit

# --- 现代化工具集成 ---
# Oh My Posh主题配置
if command -v oh-my-posh &> /dev/null; then
    eval "$(oh-my-posh init zsh --config ~/.poshthemes/catppuccin.omp.json)"
fi

# zoxide智能cd集成
if command -v zoxide &> /dev/null; then
    eval "$(zoxide init zsh)"
fi

# --- 现代化别名配置 ---
# 使用eza替代ls
if command -v eza &> /dev/null; then
    alias ls='eza --icons --group-directories-first'
    alias la='eza -a --icons --group-directories-first'
    alias ll='eza -al --icons --header --git --group-directories-first'
fi

# 使用bat替代cat
if command -v bat &> /dev/null; then
    alias cat='bat --paging=never --style=plain'
fi

# 其他实用别名
alias cp='cp -r'

# --- 用户自定义配置区域 ---
# 在此处添加您的个人配置
# 例如：export EDITOR=vim

EOF

        success "全新的.zshrc配置已生成完成。"
        info "配置特点："
        info "  - 完全干净，无多余注释"
        info "  - 包含现代化CLI工具集成"
        info "  - 模块化且易于维护"
        info "  - 支持用户自定义扩展"
    }

    info "--- 开始配置现代化终端环境 ---"

    # 3.1 安装并配置Zsh为默认Shell
    if [[ "$SHELL" != "/bin/zsh" && "$SHELL" != "/usr/bin/zsh" ]]; then
        info "正在设置Zsh为默认Shell..."
        sudo_with_msg "将Zsh设置为默认Shell..." chsh -s "$(which zsh)" "$USER"
        success "Zsh已设置为默认Shell。请重新登录以生效。"
    else
        info "Zsh已经是默认Shell，跳过。"
    fi

    # 3.2 安装Oh My Zsh (保持现有.zshrc不被覆盖)
    if [ ! -d "$HOME/.oh-my-zsh" ]; then
        info "正在安装Oh My Zsh..."
        # 备份现有的.zshrc文件（如果存在）
        if [ -f "$HOME/.zshrc" ]; then
            info "备份现有的.zshrc文件..."
            cp "$HOME/.zshrc" "$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
        fi

        # 使用--keep-zshrc参数避免覆盖.zshrc，或者手动处理
        KEEP_ZSHRC=yes sh -c "$(curl -fsSL https://gitee.com/shmhlsy/oh-my-zsh-install.sh/raw/master/install.sh)" "" --unattended --keep-zshrc
        success "Oh My Zsh安装完成。"
    else
        info "Oh My Zsh已安装，跳过。"
    fi

    # 3.3 安装Oh My Zsh插件
    info "正在安装Oh My Zsh插件..."
    local omz_custom="${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}"
    if [ ! -d "${omz_custom}/plugins/zsh-syntax-highlighting" ]; then
        git clone https://github.com/zsh-users/zsh-syntax-highlighting.git "${omz_custom}/plugins/zsh-syntax-highlighting"
    fi
    if [ ! -d "${omz_custom}/plugins/zsh-autosuggestions" ]; then
        git clone https://github.com/zsh-users/zsh-autosuggestions "${omz_custom}/plugins/zsh-autosuggestions"
    fi
    if [ ! -d "${omz_custom}/plugins/zsh-completions" ]; then
        git clone https://github.com/zsh-users/zsh-completions "${omz_custom}/plugins/zsh-completions"
    fi
    success "Oh My Zsh插件安装检查完成。"

    # 3.4 安装并配置Oh My Posh
    info "--- 开始配置Oh My Posh ---"
    local user_bin_dir="$HOME/.local/bin"
    mkdir -p "$user_bin_dir"

    if ! command_exists oh-my-posh; then
        info "正在安装Oh My Posh到 $user_bin_dir..."
        if curl -s https://ohmyposh.dev/install.sh | bash -s -- -d "$user_bin_dir"; then
            success "Oh My Posh安装完成。"
        else
            error "Oh My Posh安装失败。"
            return 1
        fi
    else
        info "Oh My Posh已安装，正在检查更新..."
        if oh-my-posh upgrade &>/dev/null; then
            success "Oh My Posh更新检查完成。"
        else
            warn "Oh My Posh更新失败，请稍后手动运行 'oh-my-posh upgrade'"
        fi
    fi

    local omp_themes_dir="$HOME/.poshthemes"
    mkdir -p "$omp_themes_dir"
    if [ ! -f "$omp_themes_dir/catppuccin.omp.json" ]; then
        info "正在下载catppuccin.omp.json主题..."
        curl -s -o "$omp_themes_dir/catppuccin.omp.json" https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/catppuccin.omp.json
        success "catppuccin.omp.json主题下载完成。"
    else
        info "catppuccin.omp.json主题已存在，跳过下载。"
    fi

    # 3.5 安装Nerd Fonts字体
    info "--- 检查并安装 Nerd Fonts ---"
    local font_dir="/usr/local/share/fonts/nerd-fonts"

    # 使用更可靠的字体检测方法：检查系统字体缓存和实际文件
    local font_installed=false

    # 方法1：检查fc-list输出（最可靠）
    if command_exists fc-list && fc-list | grep -qi "robotomono.*nerd" &>/dev/null; then
        font_installed=true
        info "通过系统字体缓存检测到RobotoMono Nerd Font"
    fi

    # 方法2：检查字体目录中是否有相关文件（备用检查）
    if [ "$font_installed" = false ] && [ -d "$font_dir" ]; then
        if find "$font_dir" -name "*RobotoMono*Nerd*" -type f | grep -q "\.ttf$" &>/dev/null; then
            font_installed=true
            info "在字体目录中检测到RobotoMono Nerd Font文件"
            # 字体文件存在但系统未识别，更新字体缓存
            info "更新字体缓存以确保系统识别..."
            sudo fc-cache -f -v &>/dev/null
        fi
    fi

    if [ "$font_installed" = true ]; then
        success "RobotoMono Nerd Font已安装，跳过下载"
    else
        # 字体未安装，开始下载安装流程
        info "下载并安装RobotoMono Nerd Font..."
        sudo mkdir -p "$font_dir"

        local temp_dir
        temp_dir=$(mktemp -d) || { error "无法创建临时目录"; return 1; }

        local font_url="https://github.com/ryanoasis/nerd-fonts/releases/download/v3.4.0/RobotoMono.zip"

        info "正在下载字体..."
        if curl -L "$font_url" -o "$temp_dir/RobotoMono.zip"; then
            info "字体下载成功，正在解压安装..."
            if unzip -qo "$temp_dir/RobotoMono.zip" -d "$temp_dir"; then
                # 安装所有TTF字体文件
                local installed_count=0
                while IFS= read -r -d '' font_file; do
                    sudo cp "$font_file" "$font_dir/"
                    ((installed_count++))
                done < <(find "$temp_dir" -name "*.ttf" -type f -print0)

                if [ "$installed_count" -gt 0 ]; then
                    info "已安装 $installed_count 个字体文件"
                    # 更新字体缓存
                    info "正在更新字体缓存..."
                    sudo fc-cache -f -v &>/dev/null
                    success "RobotoMono Nerd Font安装完成"
                else
                    warn "未找到可安装的字体文件"
                fi
            else
                warn "字体解压失败"
            fi
        else
            warn "字体下载失败，将跳过字体安装"
        fi

        # 清理临时文件
        rm -rf "$temp_dir"
    fi
    info "请在Windows终端中设置字体为 'RobotoMono Nerd Font'"

    # 3.6 安装新锐CLI工具集
    info "--- 开始安装新锐CLI工具集 ---"
    sudo_with_msg "安装新锐CLI工具集 (eza, bat, ripgrep, fd-find, zoxide, fzf, btop, jq)..." apt-get install -y eza bat ripgrep fd-find zoxide fzf btop jq
    if command_exists batcat && ! command_exists bat; then
        sudo_with_msg "创建bat符号链接..." ln -s /usr/bin/batcat /usr/local/bin/bat
    fi
    if command_exists fdfind && ! command_exists fd; then
        sudo_with_msg "创建fd符号链接..." ln -s /usr/bin/fdfind /usr/local/bin/fd
    fi
    success "新锐CLI工具集安装完成。"

    # 3.7 创建全新的.zshrc配置
    _create_clean_zshrc

    info "--- 现代化终端环境配置完成 ---"
}
