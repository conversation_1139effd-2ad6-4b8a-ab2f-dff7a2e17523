#!/usr/bin/env bash

# ==============================================================================
# 7. Dotfiles版本化与工作流
# ==============================================================================
setup_dotfiles_workflow() {
    local dotfiles_repo="https://github.com/ibootz/dotfiles.git"
    local dotfiles_dir="$HOME/dotfiles"

    info "--- 开始配置Dotfiles工作流 (使用GNU Stow) ---"

    # 6.1 安装GNU Stow
    if ! command_exists stow; then
        info "正在安装GNU Stow..."
        sudo_with_msg "安装GNU Stow..." apt-get install -y stow
    else
        info "GNU Stow 已安装，跳过。"
    fi

    # 6.2 克隆dotfiles仓库
    if [ ! -d "$dotfiles_dir" ]; then
        info "正在克隆dotfiles仓库..."
        git clone "$dotfiles_repo" "$dotfiles_dir"
        if [ $? -ne 0 ]; then
            error "无法克隆dotfiles仓库"
            return 1
        fi
    else
        info "dotfiles仓库已存在，跳过克隆。"
        info "检查dotfiles仓库状态..."

        # 检查是否有本地更改需要提交
        (cd "$dotfiles_dir" && git status --porcelain | grep -q .) && has_changes=true || has_changes=false

        # 检查是否有远程分支
        (cd "$dotfiles_dir" && git ls-remote --heads origin | grep -q .) && has_remote_branches=true || has_remote_branches=false

        # 检查是否有本地提交
        (cd "$dotfiles_dir" && git rev-parse --verify HEAD >/dev/null 2>&1) && has_commits=true || has_commits=false

        if $has_remote_branches; then
            # 远程有分支，正常更新
            info "更新dotfiles仓库..."
            (cd "$dotfiles_dir" && git pull)
        else
            # 远程没有分支
            if $has_changes || ! $has_commits; then
                # 有未提交的更改或没有提交记录
                info "检测到dotfiles仓库需要初始化远程分支..."
                info "将首先提交本地更改并推送到远程仓库..."

                # 如果没有提交，先初始化一个提交
                if ! $has_commits; then
                    (cd "$dotfiles_dir" && git add . && git commit -m "初始化dotfiles仓库")
                elif $has_changes; then
                    # 有更改但已有提交
                    (cd "$dotfiles_dir" && git add . && git commit -m "更新dotfiles配置")
                fi

                # 推送到远程，创建main分支
                info "推送到远程仓库，创建main分支..."
                (cd "$dotfiles_dir" && git push -u origin main)

                if [ $? -eq 0 ]; then
                    success "成功初始化远程仓库分支。"
                else
                    error "推送到远程仓库失败，请检查权限和网络连接。"
                    warn "继续执行脚本，但远程同步可能不完整。"
                fi
            else
                # 有提交但远程没有分支，尝试推送
                info "本地仓库有提交，但远程没有对应分支，尝试推送..."
                (cd "$dotfiles_dir" && git push -u origin main)
            fi
        fi
    fi

    # 6.3 备份现有配置文件
    info "备份现有配置文件..."
    local backup_dir="$HOME/dotfiles_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 6.3.1 定义需要备份的配置文件和目录
    local config_paths=(
        # 家目录下的常见配置文件
        "$HOME/.bashrc"
        "$HOME/.zshrc"
        "$HOME/.gitconfig"
        "$HOME/.npmrc"
        "$HOME/.vim"
        "$HOME/.vimrc"
        "$HOME/.viminfo"
        "$HOME/.ssh/config"
        "$HOME/.ssh/known_hosts"
        "$HOME/.ssh/authorized_keys"
        "$HOME/.gitignore_global"
        "$HOME/.inputrc"
        "$HOME/.profile"
        "$HOME/.bash_profile"
        "$HOME/.bash_logout"
        "$HOME/.bash_aliases"
        "$HOME/.tmux.conf"
        "$HOME/.p10k.zsh"

        # 常见配置目录
        "$HOME/.config"
        "$HOME/.local/share/fonts"
        "$HOME/.oh-my-zsh/custom"

        # 其他常见配置文件
        "$HOME/.docker/config.json"
        "$HOME/.kube/config"
        "$HOME/.npmrc"
        "$HOME/.yarnrc"
        "$HOME/.wgetrc"
        "$HOME/.curlrc"
    )

    # 6.3.2 创建备份
    info "开始备份现有配置文件到: $backup_dir"
    for path in "${config_paths[@]}"; do
        if [ -e "$path" ]; then
            # 创建目标目录结构
            local rel_path="${path#$HOME/}"
            local target_dir="$(dirname "$backup_dir/$rel_path")"
            mkdir -p "$target_dir"

            # 移动文件/目录
            if [ -L "$path" ]; then
                # 如果是符号链接，只复制链接目标
                info "备份符号链接: $path -> $(readlink -f "$path")"
                cp -P "$path" "$backup_dir/$rel_path"
            elif [ -f "$path" ]; then
                # 普通文件
                info "备份文件: $path"
                cp -a "$path" "$backup_dir/$rel_path"
            elif [ -d "$path" ]; then
                # 目录
                info "备份目录: $path"
                cp -a "$path" "$backup_dir/$(basename "$path")"
            fi
        fi
    done
    success "配置文件备份完成: $backup_dir"

    # 6.4 检查dotfiles仓库是否为空
    if [ ! -d "$dotfiles_dir/.git" ] || [ -z "$(ls -A "$dotfiles_dir" | grep -v '^\.git$')" ]; then
        info "检测到dotfiles仓库为空，准备初始化..."

        # 创建基本目录结构
        mkdir -p "$dotfiles_dir/home"
        mkdir -p "$dotfiles_dir/config"
        mkdir -p "$dotfiles_dir/ssh"

        # 创建README.md
        cat > "$dotfiles_dir/README.md" << 'EOL'
# Dotfiles

This repository contains my dotfiles managed with GNU Stow.

## Structure

- `home/`: Files that go directly in the home directory (e.g., `~/.bashrc`)
- `config/`: Files that go in `~/.config/`
- `ssh/`: SSH configuration files

## Setup

1. Clone this repository to `~/dotfiles`
2. Run `./install.sh` to set up symlinks

## Adding new dotfiles

1. Place the file in the appropriate directory structure under `home/`
2. Run `stow -v -t ~ home` to create the symlink

## Updating

```bash
git pull
./install.sh
```
EOL

        # 创建安装脚本
        cat > "$dotfiles_dir/install.sh" << 'EOL'
#!/usr/bin/env bash

set -e

DOTFILES_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 确保 stow 已安装
if ! command -v stow >/dev/null 2>&1; then
    echo "Error: GNU Stow is not installed."
    exit 1
fi

# 创建符号链接
echo "Setting up dotfiles..."

# 处理 home 目录下的文件
if [ -d "$DOTFILES_DIR/home" ]; then
    echo "Setting up home directory files..."
    for dir in "$DOTFILES_DIR/home/"*; do
        if [ -d "$dir" ]; then
            stow -v -t "$HOME" -d "$DOTFILES_DIR/home" "$(basename "$dir")" || true
        fi
    done
fi

# 处理 .config 目录
if [ -d "$DOTFILES_DIR/config" ]; then
    echo "Setting up config files..."
    mkdir -p "$HOME/.config"
    for dir in "$DOTFILES_DIR/config/"*; do
        if [ -d "$dir" ]; then
            stow -v -t "$HOME/.config" -d "$DOTFILES_DIR/config" "$(basename "$dir")" || true
        fi
    done
fi

echo "Dotfiles setup complete!"
EOL

        # 添加执行权限
        chmod +x "$dotfiles_dir/install.sh"

        # 创建 .gitignore
        cat > "$dotfiles_dir/.gitignore" << 'EOL'
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.idea
.vscode
*.swp
*.swo
*~

# Sensitive data
id_rsa
id_dsa
*.pem
*.key
*.cert
*.p12
*.crt
*.cer
*.p7b
*.p7c
*.p12
*.pfx
*.pem
*.crt
*.key
*.*~
*\#*

# Local environment files
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development files
*.local

# Cache directories
.cache/
.parcel-cache/

# Dependency directories
node_modules/

# Local Netlify folder
.netlify
EOL

        # 创建初始目录结构
        mkdir -p "$dotfiles_dir/home/<USER>"
        mkdir -p "$dotfiles_dir/home/<USER>"
        mkdir -p "$dotfiles_dir/home/<USER>"
        mkdir -p "$dotfiles_dir/config/nvim"
        mkdir -p "$dotfiles_dir/ssh"

        # 创建示例 stow 结构
        mkdir -p "$dotfiles_dir/home/<USER>/.local/share/zsh"
        touch "$dotfiles_dir/home/<USER>/.zshrc"
        touch "$dotfiles_dir/home/<USER>/.p10k.zsh"

        mkdir -p "$dotfiles_dir/home/<USER>/.vim"
        touch "$dotfiles_dir/home/<USER>/.vimrc"

        touch "$dotfiles_dir/home/<USER>/.gitconfig"
        touch "$dotfiles_dir/home/<USER>/.gitignore_global"

        # 添加初始提交
        (
            cd "$dotfiles_dir" &&
            git init &&
            git add . &&
            git commit -m "Initial commit: Dotfiles repository structure"
        )

        info "已初始化dotfiles仓库结构"
        info "请检查 $dotfiles_dir 中的配置文件，然后提交到远程仓库"
    fi

    # 6.5 使用Stow创建符号链接
    info "使用GNU Stow创建符号链接..."

    # 运行安装脚本（如果存在）
    if [ -f "$dotfiles_dir/install.sh" ]; then
        info "运行安装脚本..."
        (cd "$dotfiles_dir" && ./install.sh)
    else
        # 回退到直接使用stow
        cd "$dotfiles_dir" || { error "无法进入dotfiles目录"; return 1; }

        # 为每个顶级目录运行stow
        for dir in */; do
            if [ -d "$dir" ] && [ "$dir" != ".git/" ]; then
                dir=${dir%/}
                info "设置 $dir 配置..."

                # 根据目录类型设置目标目录
                local target_dir="$HOME"
                if [ "$dir" = "config" ]; then
                    target_dir="$HOME/.config"
                    mkdir -p "$target_dir"
                elif [ "$dir" = "ssh" ]; then
                    target_dir="$HOME/.ssh"
                    mkdir -p "$target_dir"
                    chmod 700 "$target_dir"
                fi

                # 使用stow创建符号链接
                stow -v -R "$dir" --target="$target_dir" --ignore='\.gitkeep' --ignore='README.*' || {
                    warn "设置 $dir 配置时出现问题"
                }
            fi
        done
    fi

    # 6.6 确保 .local/bin 在PATH中
    if ! grep -q "PATH=\"\\$HOME/.local/bin:\\$PATH\"" "$HOME/.zshrc"; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.zshrc"
    fi

    # 6.7 添加 dotfiles 管理命令到 PATH
    if ! grep -q "# Add dotfiles bin to PATH" "$HOME/.zshrc"; then
        cat >> "$HOME/.zshrc" << 'EOL'

# Add dotfiles bin to PATH
if [ -d "$HOME/dotfiles/bin" ]; then
    export PATH="$HOME/dotfiles/bin:$PATH"
fi
EOL
    fi

    # 6.8 添加Stow管理脚本
    if ! grep -q "# --- Stow dotfiles管理 ---" "$HOME/.zshrc"; then
        info "添加Stow管理脚本到.zshrc..."
        tee -a "$HOME/.zshrc" > /dev/null <<'EOF'

# --- Stow dotfiles管理 ---
# 更新dotfiles仓库并重新stow
function dotfiles-update {
    local dotfiles_dir="$HOME/dotfiles"
    if [ -d "$dotfiles_dir" ]; then
        echo "Updating dotfiles from $dotfiles_dir..."
        (cd "$dotfiles_dir" && git pull && echo "Dotfiles updated successfully.")
    else
        echo "Error: $dotfiles_dir not found."
        return 1
    fi
}

# 重新应用所有stow配置
function dotfiles-apply {
    local dotfiles_dir="$HOME/dotfiles"
    if [ -d "$dotfiles_dir" ]; then
        echo "Applying dotfiles from $dotfiles_dir..."
        cd "$dotfiles_dir" || return 1
        for dir in */; do
            if [ -d "$dir" ]; then
                dir=${dir%/}
                echo "Applying $dir..."
                stow -v -R "$dir" --target="$HOME"
            fi
        done
        echo "Dotfiles applied successfully."
    else
        echo "Error: $dotfiles_dir not found."
        return 1
    fi
}

# 列出所有stow管理的包
function dotfiles-list {
    local dotfiles_dir="$HOME/dotfiles"
    if [ -d "$dotfiles_dir" ]; then
        echo "Available dotfiles packages in $dotfiles_dir:"
        ls -1 "$dotfiles_dir" | grep -v '^\..*$'
    else
        echo "Error: $dotfiles_dir not found."
        return 1
    fi
}
EOF
        success "已添加Stow管理脚本。"
    fi

    success "Dotfiles工作流配置完成。"
    info "已使用GNU Stow将dotfiles从 $dotfiles_repo 链接到您的主目录。"
    info "使用 'dotfiles-update' 更新仓库，'dotfiles-apply' 重新应用配置。"
    echo
    info "下一步操作建议："
    info "1. 检查 $dotfiles_dir 中的配置文件"
    info "2. 将现有配置复制到相应目录（如 ~/.zshrc 复制到 $dotfiles_dir/home/<USER>/.zshrc）"
    info "3. 提交更改到Git仓库："
    info "   cd $dotfiles_dir"
    info "   git add ."
    info "   git commit -m 'Add initial dotfiles'"
    info "   git remote add origin $dotfiles_repo"
    info "   git push -u origin main"
    info "4. 在其他机器上克隆仓库并运行安装脚本即可恢复配置"
    echo
    info "--- Dotfiles工作流配置完成 ---"
}
