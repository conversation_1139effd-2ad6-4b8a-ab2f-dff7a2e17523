#!/usr/bin/env bash

# 为使脚本可独立运行，并去除日志，定义以下辅助函数
info() {
    echo "INFO: $1" # 日志功能已禁用，可根据需要取消注释以进行调试
    return 0
}

success() {
    echo "SUCCESS: $1" # 日志功能已禁用
    return 0
}

sudo_with_msg() {
    shift # 移除消息参数，直接执行命令
    sudo "$@"
}

setup_system_init() {
    info "--- 开始系统初始化与配置 ---"

    # 1.5 配置Git
    info "正在配置Git全局设置以优化性能..."
    git config --global clone.defaultDepth 1
    git config --global core.fscache true
    git config --global core.compression 1
    success "Git全局配置完成。"

    # 1.6 系统参数优化
    info "正在优化系统内核参数..."

    # 检查是否已经设置了vm.overcommit_memory
    if ! grep -q "^vm.overcommit_memory = 1" /etc/sysctl.conf; then
        info "设置 vm.overcommit_memory = 1 以优化内存分配策略..."
        echo "vm.overcommit_memory = 1" | sudo_with_msg "添加系统优化参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "内存分配策略优化完成。"
    else
        info "vm.overcommit_memory 已设置为 1，跳过。"
    fi

    # 设置文件描述符限制
    if ! grep -q "^fs.file-max" /etc/sysctl.conf; then
        info "增加系统最大文件描述符数量..."
        echo "fs.file-max = 655350" | sudo_with_msg "添加文件描述符限制参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "文件描述符限制已优化。"
    else
        info "文件描述符限制已配置，跳过。"
    fi

    # 优化网络参数
    if ! grep -q "^net.ipv4.tcp_fin_timeout" /etc/sysctl.conf; then
        info "优化网络连接参数..."
        cat << EOF | sudo_with_msg "添加网络优化参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
# 优化网络连接
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
EOF
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "网络连接参数优化完成。"
    else
        info "网络连接参数已配置，跳过。"
    fi

    # 调整虚拟内存交换行为 (Swappiness)
    if ! grep -q "^vm.swappiness" /etc/sysctl.conf; then
        info "降低系统 Swap 使用频率 (vm.swappiness=10)..."
        echo -e "\n# 降低系统 Swap 使用频率，在内存充足时提升性能\nvm.swappiness = 10" | sudo_with_msg "添加 Swappiness 参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "Swap 使用频率已优化。"
    else
        info "Swappiness 参数已配置，跳过。"
    fi

    # 优化 VFS 缓存压力
    if ! grep -q "^vm.vfs_cache_pressure" /etc/sysctl.conf; then
        info "优化文件系统缓存压力 (vm.vfs_cache_pressure=50)..."
        echo -e "\n# 优化 VFS 缓存回收策略，加快文件操作\nvm.vfs_cache_pressure = 50" | sudo_with_msg "添加 VFS 缓存压力参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "VFS 缓存压力已优化。"
    else
        info "VFS 缓存压力参数已配置，跳过。"
    fi

    # 提高用户级资源限制
    info "正在提高用户级资源限制 (nofile, nproc)..."
    local limits_conf="/etc/security/limits.conf"
    if ! grep -q "^\* soft nofile 65535" "$limits_conf"; then
        echo -e "\n# 提高所有用户的最大文件打开数和进程数\n* soft nofile 65535\n* hard nofile 65535\n* soft nproc 65535\n* hard nproc 65535" | sudo_with_msg "更新 $limits_conf..." tee -a "$limits_conf" > /dev/null
        success "用户级资源限制已提高。"
    else
        info "用户级资源限制已配置，跳过。"
    fi

    success "系统参数优化完成。"
    info "--- 系统初始化与配置完成 ---"
}

setup_system_init
