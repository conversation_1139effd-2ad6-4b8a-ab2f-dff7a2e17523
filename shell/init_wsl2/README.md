# WSL2 Ubuntu 全能开发环境自动化配置脚本

这是一个模块化的WSL2 Ubuntu开发环境自动配置脚本，用于快速搭建完整的开发环境。

## 功能特性

- **系统初始化**：配置国内镜像源、启用Systemd、设置时区和语言环境
- **目录规划**：创建标准化的工作目录结构
- **终端工具**：安装和配置Zsh、Oh My Zsh、Oh My Posh、现代CLI工具
- **开发环境**：通过asdf管理多语言版本（Java、Node.js、Python、Go、Rust）
- **Docker支持**：安装Docker并配置国内镜像加速
- **开发工具**：配置Git、SSH、包管理器镜像等

## 使用方法

### 在Windows中启动（推荐）

```powershell
# 在PowerShell中运行
cd C:\Users\<USER>\workspace\scripts\shell\init_wsl2
.\start.ps1
```

### 在WSL中直接运行

```bash
# 进入脚本目录
cd ~/workspace/scripts/shell/init_wsl2

# 赋予执行权限
chmod +x main.sh modules/*.sh

# 运行脚本
./main.sh
```

## 执行模式

脚本支持三种执行模式：

1. **完整执行**（默认）：`./main.sh` 或 `./main.sh --all`
2. **单步执行**：`./main.sh --step`
3. **从指定步骤开始**：`./main.sh --from 3`

## 模块说明

1. **系统初始化** (`1_setup_system_init.sh`)
   - 配置sudo免密
   - 设置APT国内镜像源
   - 启用Systemd
   - 配置时区和语言环境
   - 安装基础开发工具

2. **目录规划** (`2_setup_directories.sh`)
   - 创建工作空间目录结构
   - 设置个人项目、客户项目、学习资料等目录

3. **终端工具** (`3_setup_terminal_tools.sh`)
   - 安装和配置Zsh
   - 安装Oh My Zsh和插件
   - 配置Oh My Posh主题
   - 安装现代CLI工具（eza、bat、ripgrep等）
   - 下载和安装Nerd Fonts

4. **开发环境** (`4_setup_asdf_dev_env.sh`)
   - 安装asdf版本管理器
   - 安装Java、Node.js、Python、Go、Rust
   - 配置包管理器国内镜像

5. **Docker配置** (`5_setup_docker.sh`)
   - 安装Docker Engine
   - 配置Docker镜像加速
   - 设置用户权限

6. **Dotfiles工作流** (`6_setup_dotfiles_workflow.sh`)
   - 配置Git和SSH
   - 设置开发工具配置文件

7. **AI工具** (`7_setup_gemini_cli.sh`)
   - 安装Gemini CLI工具

8. **最终说明** (`8_final_instructions.sh`)
   - 显示配置完成后的重要提示

## 环境要求

- Windows 11 with WSL2
- Ubuntu 20.04+ (在WSL2中)
- 网络连接（用于下载软件包）

## 故障排除

### 权限问题
```bash
chmod +x main.sh modules/*.sh
```

### 网络问题
脚本已配置国内镜像源，如遇网络问题请检查：
- WSL网络连接
- 防火墙设置
- 代理配置

### 字体显示问题
安装完成后需要：
1. 在Windows中安装Nerd Font字体
2. 在终端应用中设置字体为Nerd Font
3. 重启WSL：`wsl --shutdown`

## 自定义配置

可以通过环境变量自定义配置：

```bash
# 设置GitHub用户名
export GITHUB_USERNAME=your_username

# 设置dotfiles仓库
export DOTFILES_REPO=https://github.com/your_username/dotfiles.git

# 运行脚本
./main.sh
```

## 版本信息

- 版本：3.0.0
- 更新日期：2024年
- 支持系统：WSL2 Ubuntu 20.04+

## 贡献

欢迎提交Issue和Pull Request来改进这个脚本。
