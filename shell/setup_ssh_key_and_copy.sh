#!/bin/bash

# 此脚本用于生成SSH密钥对并将公钥复制到远程服务器
# 主要功能包括：
# 1. 检查是否已存在SSH密钥对
# 2. 如果不存在，则生成新的SSH密钥对
# 3. 提示用户输入远程服务器的用户名和主机名
# 4. 将生成的公钥复制到指定的远程服务器
# 使用方法：直接运行此脚本，按提示输入所需信息

# 检查是否已经存在 SSH 密钥对
if [ -f ~/.ssh/id_rsa ]; then
    echo "SSH 密钥对已存在，不再重复生成。"
else
    # 生成新的 SSH 密钥对
    ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
    echo "新的 SSH 密钥对已生成。"
fi

# 提示用户输入远程服务器的用户名
read -p "请输入远程服务器的用户名: " REMOTE_USER
if [ -z "$REMOTE_USER" ]; then
    echo "用户名不能为空。"
    exit 1
fi

# 提示用户输入远程服务器的主机名
read -p "请输入远程服务器的主机名或 IP 地址: " REMOTE_HOST
if [ -z "$REMOTE_HOST" ]; then
    echo "主机名不能为空。"
    exit 1
fi

# 将公钥复制到远程服务器
ssh-copy-id -i ~/.ssh/id_rsa.pub $REMOTE_USER@$REMOTE_HOST
echo "公钥已复制到远程服务器。"

