#!/bin/bash

source $(dirname "$0")/00_init_base.sh

log "安装和配置Docker"
dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
systemctl enable docker
systemctl start docker

# 获取当前登录的非root用户
CURRENT_USER=$(who am i | awk '{print $1}')

if [ "$CURRENT_USER" != "root" ] && [ -n "$CURRENT_USER" ]; then
    usermod -aG docker $CURRENT_USER
    log "用户 $CURRENT_USER 已添加到 docker 组"
else
    log "当前以 root 用户运行，跳过将用户添加到 docker 组的步骤"
fi

log "Docker 安装和配置完成"
