#!/bin/bash

source $(dirname "$0")/00_init_base.sh

# 设置rockylinux9国内镜像(国外云服务器无需配置)
log "设置国内镜像"
REPO_FILES=$(ls /etc/yum.repos.d/rocky*.repo 2>/dev/null)
if [ -z "$REPO_FILES" ]; then
    echo "警告: 未找到匹配的仓库文件"
else
    for repo_file in $REPO_FILES; do
        if [ -f "$repo_file" ]; then
            sed -e 's|^mirrorlist=|#mirrorlist=|g' \
                -e 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' \
                -i.bak \
                "$repo_file"
            echo "已更新仓库文件: $repo_file"
        else
            echo "警告: 仓库文件 $repo_file 不存在"
        fi
    done
fi

