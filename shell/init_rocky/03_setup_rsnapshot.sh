#!/bin/bash

source $(dirname "$0")/00_init_base.sh

log "安装rsnapshot"
dnf install -y rsnapshot rsync

log "配置rsnapshot"
cat << 'EOF' > /etc/rsnapshot.conf
#注意key和value之间必须使用tab分隔,配置好之后可以使用`rsnapshot configtest`命令检查一遍

#指定rsync路径
cmd_rsync	/usr/bin/rsync

#rsnapshot配置版本
config_version	1.2

#备份存储的根目录
snapshot_root	/backup/

#如果根目录不存在,自动创建
no_create_root	0

#保留策略设置
retain	daily	3	#保留3个每日备份
retain	weekly	1	#保留1个每周备份
retain	monthly	1	#保留1个每月备份

#详细输出级别
verbose	2

#日志级别
loglevel	3

#锁文件位置,防止多个rsnapshot实例同时运行
lockfile	/var/run/rsnapshot.pid

#rsync短参数
rsync_short_args	-avz

#rsync长参数
rsync_long_args	--delete --numeric-ids --relative

# 排除规则
exclude	/dev/*
exclude	/proc/*
exclude	/sys/*
exclude	/tmp/*
exclude	/run/*
exclude	/mnt/*
exclude	/media/*
exclude	/lost+found
exclude	/backup/*

#备份整个根目录到localhost/,最终备份目录为/backup/localhost/
backup	/	localhost/
EOF

log "设置定时备份任务"
(
  crontab -l 2>/dev/null
  echo "0 2 * * * /usr/bin/rsnapshot daily"
  echo "0 3 * * 0 /usr/bin/rsnapshot weekly"
  echo "0 4 1 * * /usr/bin/rsnapshot monthly"
) | crontab -

log "首次全量备份"
rsnapshot daily
