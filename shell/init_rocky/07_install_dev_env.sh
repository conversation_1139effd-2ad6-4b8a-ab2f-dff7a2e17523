#!/bin/bash

source $(dirname "$0")/00_init_base.sh

log "安装Node环境"
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
source $HOME/.bashrc
nvm install --lts
npm install -g yarn pnpm

log "安装Java环境"
dnf install -y java-17-openjdk java-21-openjdk
ln -s /usr/lib/jvm/java-17-openjdk-* /usr/lib/jvm/java-17-openjdk
ln -s /usr/lib/jvm/java-21-openjdk-* /usr/lib/jvm/java-21-openjdk
git clone https://github.com/jenv/jenv.git ~/.jenv
echo 'export PATH="$HOME/.jenv/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(jenv init -)"' >> ~/.bashrc
source ~/.bashrc
jenv enable-plugin export
jenv add /usr/lib/jvm/java-17-openjdk
jenv add /usr/lib/jvm/java-21-openjdk
jenv global 21

log "安装Maven"
MAVEN_VERSION=$(curl -s https://maven.apache.org/download.cgi | grep -oP 'Apache Maven \K[0-9.]+' | head -1)
curl -O https://dlcdn.apache.org/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz
tar -xzf apache-maven-${MAVEN_VERSION}-bin.tar.gz
mv apache-maven-${MAVEN_VERSION} /opt/maven
ln -s /opt/maven/bin/mvn /usr/bin/mvn
rm -rf apache-maven-${MAVEN_VERSION}-bin.tar.gz

log "安装go环境"
dnf install -y golang

log "安装python环境"
dnf install -y python3 python3-pip

log "安装rust环境"
dnf install -y rust

log "安装miniconda"
# 首先安装必要的系统依赖
dnf install -y libarchive || { echo "安装 libarchive 失败"; exit 1; }

# 检查 Miniconda 是否已安装
if [ -d "$HOME/DevTools/miniconda" ]; then
  echo "Miniconda 已安装，跳过安装步骤。"
  exit 0
fi

wget https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh || { echo "下载 Miniconda 失败"; exit 1; }
bash miniconda.sh -b -p $HOME/DevTools/miniconda || { echo "安装 Miniconda 失败"; exit 1; }
rm miniconda.sh
# 初始化 conda 并设置使用 classic solver
$HOME/DevTools/miniconda/bin/conda init --all || { echo "初始化 conda 失败"; exit 1; }
$HOME/DevTools/miniconda/bin/conda config --set solver classic || { echo "设置 conda solver 失败"; exit 1; }

# 配置 conda 源
$HOME/DevTools/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
$HOME/DevTools/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
$HOME/DevTools/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
$HOME/DevTools/miniconda/bin/conda config --set show_channel_urls yes
