#!/bin/bash

source $(dirname "$0")/00_init_base.sh

log "配置系统设置"
timedatectl set-timezone Asia/Shanghai
hostnamectl set-hostname zqwork
timedatectl set-ntp true


log "系统优化"
cat << EOF >> /etc/sysctl.conf
vm.swappiness = 0
vm.max_map_count = 262144
vm.dirty_ratio = 10
vm.dirty_background_ratio = 5
vm.dirty_expire_centisecs = 500
vm.dirty_writeback_centisecs = 500
fs.file-max = 1048576
fs.nr_open = 1048576
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_max_tw_buckets = 65535
EOF
sysctl -p
